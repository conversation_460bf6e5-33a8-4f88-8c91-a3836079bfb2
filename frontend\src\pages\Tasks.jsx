import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  PlusIcon,
  SparklesIcon,
  ClockIcon,
  CalendarIcon,
  FlagIcon,
  CheckIcon,
  PencilIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  BoltIcon,
  ChartBarIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'
// Removed deprecated react-beautiful-dnd - using simple reordering instead
import { apiHelpers } from '@services/api'
import { useSocket } from '@context/SocketContext'
import Loading from '@components/Loading'
import toast from 'react-hot-toast'

const Tasks = () => {
  const [tasks, setTasks] = useState([])
  const [showAddTask, setShowAddTask] = useState(false)
  const [showAIInsights, setShowAIInsights] = useState(false)
  const [selectedTask, setSelectedTask] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterPriority, setFilterPriority] = useState('all')
  const [sortBy, setSortBy] = useState('ai_priority')
  const [viewMode, setViewMode] = useState('list') // list, kanban, calendar

  // New task form state
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium',
    estimatedDuration: 30,
    dueDate: '',
    tags: [],
    projectId: null,
    dependencies: []
  })

  const queryClient = useQueryClient()
  const { socket, isConnected } = useSocket()

  // Fetch tasks with AI prioritization
  const { data: tasksData, isLoading: tasksLoading } = useQuery(
    ['tasks', { search: searchQuery, status: filterStatus, priority: filterPriority, sort: sortBy }],
    () => apiHelpers.get('/tasks', {
      params: {
        search: searchQuery,
        status: filterStatus !== 'all' ? filterStatus : undefined,
        priority: filterPriority !== 'all' ? filterPriority : undefined,
        sort: sortBy,
        include_ai_insights: true
      }
    }),
    {
      onSuccess: (response) => {
        setTasks(response.data.data.tasks || [])
      },
      refetchInterval: 30000 // Refresh every 30 seconds for AI updates
    }
  )

  // Fetch AI insights
  const { data: aiInsights } = useQuery(
    'taskAIInsights',
    () => apiHelpers.get('/tasks/ai-insights'),
    {
      select: (response) => response.data.data,
      refetchInterval: 60000 // Refresh every minute
    }
  )

  // Fetch projects for task assignment
  const { data: projects } = useQuery(
    'projects',
    () => apiHelpers.get('/projects'),
    {
      select: (response) => response.data.data.projects || []
    }
  )

  // Create task mutation
  const createTaskMutation = useMutation(
    (taskData) => apiHelpers.post('/tasks', taskData),
    {
      onSuccess: (response) => {
        const newTask = response.data.data.task
        setTasks(prev => [newTask, ...prev])
        queryClient.invalidateQueries('tasks')
        queryClient.invalidateQueries('taskAIInsights')
        setShowAddTask(false)
        setNewTask({
          title: '',
          description: '',
          priority: 'medium',
          estimatedDuration: 30,
          dueDate: '',
          tags: [],
          projectId: null,
          dependencies: []
        })
        toast.success('Task created successfully!')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to create task')
      }
    }
  )

  // Update task mutation
  const updateTaskMutation = useMutation(
    ({ taskId, updates }) => apiHelpers.put(`/tasks/${taskId}`, updates),
    {
      onSuccess: (response) => {
        const updatedTask = response.data.data.task
        setTasks(prev => prev.map(task =>
          task.id === updatedTask.id ? updatedTask : task
        ))
        queryClient.invalidateQueries('tasks')
        queryClient.invalidateQueries('taskAIInsights')
        toast.success('Task updated successfully!')
      }
    }
  )

  // Delete task mutation
  const deleteTaskMutation = useMutation(
    (taskId) => apiHelpers.delete(`/tasks/${taskId}`),
    {
      onSuccess: (response, taskId) => {
        setTasks(prev => prev.filter(task => task.id !== taskId))
        queryClient.invalidateQueries('tasks')
        queryClient.invalidateQueries('taskAIInsights')
        toast.success('Task deleted successfully!')
      }
    }
  )

  // AI prioritization mutation
  const reprioritizeMutation = useMutation(
    () => apiHelpers.post('/tasks/ai-reprioritize'),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('tasks')
        queryClient.invalidateQueries('taskAIInsights')
        toast.success('Tasks reprioritized using AI!')
      }
    }
  )

  // Socket event listeners
  useEffect(() => {
    if (!socket || !isConnected) return

    const handleTaskCreated = (data) => {
      setTasks(prev => [data, ...prev])
      queryClient.invalidateQueries('tasks')
    }

    const handleTaskUpdated = (data) => {
      setTasks(prev => prev.map(task =>
        task.id === data.id ? data : task
      ))
      queryClient.invalidateQueries('tasks')
    }

    const handleTaskDeleted = (data) => {
      setTasks(prev => prev.filter(task => task.id !== data.id))
      queryClient.invalidateQueries('tasks')
    }

    const handleAIInsightsUpdated = () => {
      queryClient.invalidateQueries('taskAIInsights')
      queryClient.invalidateQueries('tasks')
    }

    socket.on('task-created', handleTaskCreated)
    socket.on('task-updated', handleTaskUpdated)
    socket.on('task-deleted', handleTaskDeleted)
    socket.on('ai-insights-updated', handleAIInsightsUpdated)

    return () => {
      socket.off('task-created', handleTaskCreated)
      socket.off('task-updated', handleTaskUpdated)
      socket.off('task-deleted', handleTaskDeleted)
      socket.off('ai-insights-updated', handleAIInsightsUpdated)
    }
  }, [socket, isConnected, queryClient])

  // Handle task reordering with simple up/down buttons
  const moveTask = (taskId, direction) => {
    const taskIndex = tasks.findIndex(task => task.id === taskId)
    if (taskIndex === -1) return

    const newTasks = [...tasks]
    const targetIndex = direction === 'up' ? taskIndex - 1 : taskIndex + 1

    if (targetIndex < 0 || targetIndex >= newTasks.length) return

    // Swap tasks
    [newTasks[taskIndex], newTasks[targetIndex]] = [newTasks[targetIndex], newTasks[taskIndex]]
    setTasks(newTasks)

    // Update task order on server
    updateTaskMutation.mutate({
      taskId: taskId,
      updates: { order: targetIndex }
    })
  }

  // Handle task completion toggle
  const toggleTaskCompletion = (task) => {
    const newStatus = task.status === 'completed' ? 'pending' : 'completed'
    updateTaskMutation.mutate({
      taskId: task.id,
      updates: {
        status: newStatus,
        completedAt: newStatus === 'completed' ? new Date().toISOString() : null
      }
    })
  }

  // Handle task creation
  const handleCreateTask = () => {
    if (!newTask.title.trim()) {
      toast.error('Task title is required')
      return
    }

    createTaskMutation.mutate({
      ...newTask,
      tags: newTask.tags.filter(tag => tag.trim()),
      dueDate: newTask.dueDate || null,
      projectId: newTask.projectId || null
    })
  }

  // Filter and sort tasks
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || task.status === filterStatus
    const matchesPriority = filterPriority === 'all' || task.priority === filterPriority

    return matchesSearch && matchesStatus && matchesPriority
  })

  // Priority colors and icons
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100'
      case 'high': return 'text-orange-600 bg-orange-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100'
      case 'in_progress': return 'text-blue-600 bg-blue-100'
      case 'pending': return 'text-gray-600 bg-gray-100'
      case 'blocked': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const formatDuration = (minutes) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return `${hours}h ${mins}m`
    }
    return `${minutes}m`
  }

  if (tasksLoading) {
    return <Loading fullScreen />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            AI-Powered Tasks
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Intelligent task prioritization and management
          </p>
        </div>

        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button
            onClick={() => reprioritizeMutation.mutate()}
            disabled={reprioritizeMutation.isLoading}
            className="btn btn-secondary flex items-center"
          >
            <SparklesIcon className="h-4 w-4 mr-2" />
            {reprioritizeMutation.isLoading ? 'Reprioritizing...' : 'AI Reprioritize'}
          </button>

          <button
            onClick={() => setShowAIInsights(true)}
            className="btn btn-secondary flex items-center"
          >
            <ChartBarIcon className="h-4 w-4 mr-2" />
            AI Insights
          </button>

          <button
            onClick={() => setShowAddTask(true)}
            className="btn btn-primary flex items-center"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Task
          </button>
        </div>
      </div>

      {/* AI Insights Banner */}
      {aiInsights && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800"
        >
          <div className="flex items-start space-x-3">
            <BoltIcon className="h-6 w-6 text-blue-600 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                AI Productivity Insights
              </h3>
              <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-blue-700 dark:text-blue-300">Peak Hours:</span>
                  <span className="ml-2 font-semibold">{aiInsights.peakHours?.join(', ') || 'Analyzing...'}</span>
                </div>
                <div>
                  <span className="text-blue-700 dark:text-blue-300">Completion Rate:</span>
                  <span className="ml-2 font-semibold">{aiInsights.completionRate || 0}%</span>
                </div>
                <div>
                  <span className="text-blue-700 dark:text-blue-300">Avg Task Duration:</span>
                  <span className="ml-2 font-semibold">{formatDuration(aiInsights.avgDuration || 0)}</span>
                </div>
              </div>
              {aiInsights.recommendation && (
                <p className="mt-2 text-sm text-blue-800 dark:text-blue-200">
                  💡 <strong>AI Recommendation:</strong> {aiInsights.recommendation}
                </p>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* Filters and Search */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10 w-full"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FunnelIcon className="h-4 w-4 text-gray-500" />
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="form-select text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="blocked">Blocked</option>
                </select>
              </div>

              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                className="form-select text-sm"
              >
                <option value="all">All Priority</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="form-select text-sm"
              >
                <option value="ai_priority">AI Priority</option>
                <option value="due_date">Due Date</option>
                <option value="created_at">Created Date</option>
                <option value="priority">Manual Priority</option>
                <option value="duration">Duration</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Tasks List */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Tasks ({filteredTasks.length})
            </h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
              >
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </button>
              <button
                onClick={() => setViewMode('kanban')}
                className={`p-2 rounded ${viewMode === 'kanban' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
              >
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="card-body p-0">
          {filteredTasks.length === 0 ? (
            <div className="text-center py-12">
              <SparklesIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No tasks found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {searchQuery || filterStatus !== 'all' || filterPriority !== 'all'
                  ? 'Try adjusting your filters or search query'
                  : 'Get started by creating your first task'
                }
              </p>
              {!searchQuery && filterStatus === 'all' && filterPriority === 'all' && (
                <button
                  onClick={() => setShowAddTask(true)}
                  className="btn btn-primary"
                >
                  Create First Task
                </button>
              )}
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTasks.map((task, index) => (
                <div
                  key={task.id}
                  className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-start space-x-4">
                    {/* Reorder Controls */}
                    <div className="flex flex-col space-y-1 mt-1">
                      <button
                        onClick={() => moveTask(task.id, 'up')}
                        disabled={index === 0}
                        className={`p-1 rounded ${
                          index === 0
                            ? 'text-gray-300 cursor-not-allowed'
                            : 'text-gray-400 hover:text-blue-600 hover:bg-blue-50'
                        }`}
                      >
                        <ArrowUpIcon className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => moveTask(task.id, 'down')}
                        disabled={index === filteredTasks.length - 1}
                        className={`p-1 rounded ${
                          index === filteredTasks.length - 1
                            ? 'text-gray-300 cursor-not-allowed'
                            : 'text-gray-400 hover:text-blue-600 hover:bg-blue-50'
                        }`}
                      >
                        <ArrowDownIcon className="h-3 w-3" />
                      </button>
                    </div>

                              {/* Completion Checkbox */}
                              <button
                                onClick={() => toggleTaskCompletion(task)}
                                className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                                  task.status === 'completed'
                                    ? 'bg-green-500 border-green-500 text-white'
                                    : 'border-gray-300 hover:border-green-400'
                                }`}
                              >
                                {task.status === 'completed' && (
                                  <CheckIcon className="h-3 w-3" />
                                )}
                              </button>

                              {/* Task Content */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <h4 className={`text-sm font-medium ${
                                      task.status === 'completed'
                                        ? 'line-through text-gray-500'
                                        : 'text-gray-900 dark:text-gray-100'
                                    }`}>
                                      {task.title}
                                      {task.aiPriorityScore && (
                                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                          <SparklesIcon className="h-3 w-3 mr-1" />
                                          AI: {Math.round(task.aiPriorityScore)}
                                        </span>
                                      )}
                                    </h4>

                                    {task.description && (
                                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                        {task.description}
                                      </p>
                                    )}

                                    {/* Task Metadata */}
                                    <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                                      {/* Priority */}
                                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                                        <FlagIcon className="h-3 w-3 mr-1" />
                                        {task.priority}
                                      </span>

                                      {/* Status */}
                                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                                        {task.status.replace('_', ' ')}
                                      </span>

                                      {/* Duration */}
                                      {task.estimatedDuration && (
                                        <span className="flex items-center">
                                          <ClockIcon className="h-3 w-3 mr-1" />
                                          {formatDuration(task.estimatedDuration)}
                                        </span>
                                      )}

                                      {/* Due Date */}
                                      {task.dueDate && (
                                        <span className={`flex items-center ${
                                          new Date(task.dueDate) < new Date() ? 'text-red-600' : ''
                                        }`}>
                                          <CalendarIcon className="h-3 w-3 mr-1" />
                                          {new Date(task.dueDate).toLocaleDateString()}
                                        </span>
                                      )}

                                      {/* Project */}
                                      {task.project && (
                                        <span className="text-blue-600">
                                          {task.project.name}
                                        </span>
                                      )}
                                    </div>

                                    {/* Tags */}
                                    {task.tags && task.tags.length > 0 && (
                                      <div className="mt-2 flex flex-wrap gap-1">
                                        {task.tags.map((tag, tagIndex) => (
                                          <span
                                            key={tagIndex}
                                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                                          >
                                            {tag}
                                          </span>
                                        ))}
                                      </div>
                                    )}
                                  </div>

                                  {/* Actions */}
                                  <div className="flex items-center space-x-2 ml-4">
                                    <button
                                      onClick={() => setSelectedTask(task)}
                                      className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                    >
                                      <PencilIcon className="h-4 w-4" />
                                    </button>
                                    <button
                                      onClick={() => deleteTaskMutation.mutate(task.id)}
                                      className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                    >
                                      <TrashIcon className="h-4 w-4" />
                                    </button>
                                  </div>
                                </div>
                              </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add Task Modal */}
      <AnimatePresence>
        {showAddTask && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowAddTask(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
                  Create New Task
                </h3>

                <div className="space-y-4">
                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Task Title *
                    </label>
                    <input
                      type="text"
                      value={newTask.title}
                      onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                      className="form-input w-full"
                      placeholder="Enter task title..."
                      autoFocus
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Description
                    </label>
                    <textarea
                      value={newTask.description}
                      onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                      className="form-textarea w-full"
                      rows={3}
                      placeholder="Describe the task..."
                    />
                  </div>

                  {/* Priority and Duration */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Priority
                      </label>
                      <select
                        value={newTask.priority}
                        onChange={(e) => setNewTask(prev => ({ ...prev, priority: e.target.value }))}
                        className="form-select w-full"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Estimated Duration (minutes)
                      </label>
                      <input
                        type="number"
                        value={newTask.estimatedDuration}
                        onChange={(e) => setNewTask(prev => ({ ...prev, estimatedDuration: parseInt(e.target.value) || 0 }))}
                        className="form-input w-full"
                        min="5"
                        step="5"
                      />
                    </div>
                  </div>

                  {/* Due Date and Project */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Due Date
                      </label>
                      <input
                        type="datetime-local"
                        value={newTask.dueDate}
                        onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
                        className="form-input w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Project
                      </label>
                      <select
                        value={newTask.projectId || ''}
                        onChange={(e) => setNewTask(prev => ({ ...prev, projectId: e.target.value || null }))}
                        className="form-select w-full"
                      >
                        <option value="">No Project</option>
                        {projects?.map(project => (
                          <option key={project.id} value={project.id}>
                            {project.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Tags */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Tags (comma-separated)
                    </label>
                    <input
                      type="text"
                      value={newTask.tags.join(', ')}
                      onChange={(e) => setNewTask(prev => ({
                        ...prev,
                        tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                      }))}
                      className="form-input w-full"
                      placeholder="work, urgent, meeting..."
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setShowAddTask(false)}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateTask}
                    disabled={createTaskMutation.isLoading || !newTask.title.trim()}
                    className="btn btn-primary"
                  >
                    {createTaskMutation.isLoading ? 'Creating...' : 'Create Task'}
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Insights Modal */}
      <AnimatePresence>
        {showAIInsights && aiInsights && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowAIInsights(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    AI Productivity Insights
                  </h3>
                  <button
                    onClick={() => setShowAIInsights(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Performance Metrics */}
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
                      Performance Metrics
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-blue-700 dark:text-blue-300">Completion Rate</span>
                        <span className="font-semibold text-blue-900 dark:text-blue-100">
                          {aiInsights.completionRate}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-blue-700 dark:text-blue-300">Avg Duration</span>
                        <span className="font-semibold text-blue-900 dark:text-blue-100">
                          {formatDuration(aiInsights.avgDuration)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-blue-700 dark:text-blue-300">Tasks/Day</span>
                        <span className="font-semibold text-blue-900 dark:text-blue-100">
                          {aiInsights.tasksPerDay}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Peak Hours */}
                  <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-3">
                      Peak Productivity Hours
                    </h4>
                    <div className="space-y-2">
                      {aiInsights.peakHours?.slice(0, 3).map((hour, index) => (
                        <div key={hour} className="flex justify-between">
                          <span className="text-sm text-green-700 dark:text-green-300">
                            {hour}:00 - {hour + 1}:00
                          </span>
                          <span className="text-xs text-green-600 dark:text-green-400">
                            #{index + 1}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Task Patterns */}
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-3">
                      Task Patterns
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-purple-700 dark:text-purple-300">Most Productive Day</span>
                        <span className="font-semibold text-purple-900 dark:text-purple-100">
                          {aiInsights.bestDay || 'Monday'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-purple-700 dark:text-purple-300">Avg Break Time</span>
                        <span className="font-semibold text-purple-900 dark:text-purple-100">
                          {formatDuration(aiInsights.avgBreakTime || 15)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-purple-700 dark:text-purple-300">Focus Streak</span>
                        <span className="font-semibold text-purple-900 dark:text-purple-100">
                          {aiInsights.focusStreak || 0} days
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* AI Recommendations */}
                {aiInsights.recommendations && aiInsights.recommendations.length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                      AI Recommendations
                    </h4>
                    <div className="space-y-3">
                      {aiInsights.recommendations.map((rec, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                          <SparklesIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                          <div>
                            <h5 className="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                              {rec.title}
                            </h5>
                            <p className="text-sm text-yellow-800 dark:text-yellow-200 mt-1">
                              {rec.description}
                            </p>
                            {rec.impact && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-200 text-yellow-800 mt-2">
                                Impact: {rec.impact}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Productivity Trends */}
                {aiInsights.trends && (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                      Productivity Trends
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">This Week</span>
                          <span className={`text-sm font-semibold ${
                            aiInsights.trends.weeklyChange >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {aiInsights.trends.weeklyChange >= 0 ? '+' : ''}{aiInsights.trends.weeklyChange}%
                          </span>
                        </div>
                      </div>
                      <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">This Month</span>
                          <span className={`text-sm font-semibold ${
                            aiInsights.trends.monthlyChange >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {aiInsights.trends.monthlyChange >= 0 ? '+' : ''}{aiInsights.trends.monthlyChange}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export default Tasks
