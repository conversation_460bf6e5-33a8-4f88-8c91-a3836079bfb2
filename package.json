{"name": "timewarp", "version": "1.0.0", "description": "AI-Powered Productivity Management Platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:desktop": "cd desktop && npm run electron:dev", "build": "npm run build:backend && npm run build:frontend && npm run build:desktop", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:desktop": "cd desktop && npm run build", "build:extension": "cd browser-extension && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:coverage": "npm run test:backend -- --coverage && npm run test:frontend -- --coverage", "test:e2e": "cd frontend && npm run test:e2e", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "setup": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../desktop && npm install && cd ..", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down"}, "keywords": ["productivity", "time-tracking", "ai", "machine-learning", "scheduling", "focus", "analytics", "collaboration"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/TimeWarp.git"}, "bugs": {"url": "https://github.com/HectorTa1989/TimeWarp/issues"}, "homepage": "https://github.com/HectorTa1989/TimeWarp#readme", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}