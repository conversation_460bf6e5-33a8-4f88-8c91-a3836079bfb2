{"name": "timewarp-backend", "version": "1.0.0", "description": "TimeWarp Backend API Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Backend build complete'", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src/ --ext .js,.jsx,.ts,.tsx --fix", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "seed": "sequelize-cli db:seed:all", "seed:undo": "sequelize-cli db:seed:undo:all", "db:create": "sequelize-cli db:create", "db:drop": "sequelize-cli db:drop"}, "dependencies": {"@azure/msal-node": "^2.5.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.6", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "googleapis": "^129.0.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "redis": "^4.6.11", "sequelize": "^6.35.2", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.1.0", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/migrations/**", "!src/seeders/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["api", "backend", "productivity", "time-tracking", "ai"], "author": "HectorTa1989", "license": "MIT"}