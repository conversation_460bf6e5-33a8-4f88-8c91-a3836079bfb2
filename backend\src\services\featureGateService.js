const { Subscription, User } = require('../models')

class FeatureGateService {
  constructor() {
    this.features = {
      // Core features
      'projects': { type: 'limit', freeLimit: 3 },
      'teamMembers': { type: 'limit', freeLimit: 1 },
      'workspaces': { type: 'limit', freeLimit: 1 },
      'storageGB': { type: 'limit', freeLimit: 1 },
      'timeTrackingHours': { type: 'limit', freeLimit: 40 },
      
      // Premium features
      'advancedAnalytics': { type: 'boolean', freeAccess: false },
      'teamCollaboration': { type: 'boolean', freeAccess: false },
      'prioritySupport': { type: 'boolean', freeAccess: false },
      'customIntegrations': { type: 'boolean', freeAccess: false },
      'exportData': { type: 'boolean', freeAccess: false },
      'apiAccess': { type: 'boolean', freeAccess: false },
      'whiteLabel': { type: 'boolean', freeAccess: false },
      'sso': { type: 'boolean', freeAccess: false },
      'auditLogs': { type: 'boolean', freeAccess: false },
      
      // Advanced features
      'customReports': { type: 'boolean', freeAccess: false },
      'automatedWorkflows': { type: 'boolean', freeAccess: false },
      'advancedPermissions': { type: 'boolean', freeAccess: false },
      'bulkOperations': { type: 'boolean', freeAccess: false },
      'dataRetention': { type: 'limit', freeLimit: 30 }, // days
      'emailSupport': { type: 'boolean', freeAccess: true },
      'phoneSupport': { type: 'boolean', freeAccess: false },
      'onboarding': { type: 'boolean', freeAccess: false }
    }
    
    this.upgradePrompts = {
      'projects': {
        title: 'Upgrade to Create More Projects',
        message: 'You\'ve reached your project limit. Upgrade to Team plan for 25 projects or Business plan for 100 projects.',
        cta: 'Upgrade Now',
        urgency: 'high'
      },
      'teamMembers': {
        title: 'Invite Team Members',
        message: 'Collaborate with your team! Upgrade to add up to 10 team members with Team plan.',
        cta: 'Upgrade to Team',
        urgency: 'medium'
      },
      'advancedAnalytics': {
        title: 'Unlock Advanced Analytics',
        message: 'Get detailed insights into your productivity with advanced analytics and custom reports.',
        cta: 'See Analytics',
        urgency: 'low'
      },
      'exportData': {
        title: 'Export Your Data',
        message: 'Export your time tracking data in multiple formats. Available with Team plan and above.',
        cta: 'Upgrade to Export',
        urgency: 'medium'
      }
    }
  }

  async getUserSubscription(userId) {
    const subscription = await Subscription.findOne({
      where: { userId, status: ['active', 'trialing'] },
      order: [['createdAt', 'DESC']]
    })
    
    return subscription || this.createFreeSubscription(userId)
  }

  async createFreeSubscription(userId) {
    return await Subscription.create({
      userId,
      plan: 'free',
      status: 'active'
    })
  }

  async checkFeatureAccess(userId, feature) {
    const subscription = await this.getUserSubscription(userId)
    
    if (!this.features[feature]) {
      throw new Error(`Unknown feature: ${feature}`)
    }

    const featureConfig = this.features[feature]
    
    if (featureConfig.type === 'boolean') {
      return subscription.hasFeature(feature)
    }
    
    if (featureConfig.type === 'limit') {
      const limit = subscription.getFeatureLimit(feature)
      const usage = subscription.usage[feature] || 0
      
      return {
        hasAccess: limit === -1 || usage < limit,
        limit,
        usage,
        remaining: limit === -1 ? -1 : Math.max(0, limit - usage),
        percentage: limit === -1 ? 0 : Math.min(100, (usage / limit) * 100)
      }
    }
    
    return false
  }

  async enforceFeatureLimit(userId, feature, increment = 1) {
    const access = await this.checkFeatureAccess(userId, feature)
    
    if (this.features[feature].type === 'boolean') {
      if (!access) {
        throw new FeatureRestrictedError(feature, this.upgradePrompts[feature])
      }
      return true
    }
    
    if (this.features[feature].type === 'limit') {
      if (!access.hasAccess || access.remaining < increment) {
        throw new FeatureRestrictedError(feature, this.upgradePrompts[feature], access)
      }
      
      // Update usage
      const subscription = await this.getUserSubscription(userId)
      const newUsage = (subscription.usage[feature] || 0) + increment
      await subscription.updateUsage({ [feature]: newUsage })
      
      return {
        success: true,
        newUsage,
        remaining: access.limit === -1 ? -1 : access.limit - newUsage
      }
    }
    
    return false
  }

  async getUsageSummary(userId) {
    const subscription = await this.getUserSubscription(userId)
    const summary = {
      plan: subscription.plan,
      status: subscription.status,
      features: {},
      warnings: [],
      upgradeRecommendations: []
    }

    for (const [feature, config] of Object.entries(this.features)) {
      if (config.type === 'boolean') {
        summary.features[feature] = {
          type: 'boolean',
          hasAccess: subscription.hasFeature(feature)
        }
      } else if (config.type === 'limit') {
        const limit = subscription.getFeatureLimit(feature)
        const usage = subscription.usage[feature] || 0
        const percentage = limit === -1 ? 0 : (usage / limit) * 100
        
        summary.features[feature] = {
          type: 'limit',
          limit,
          usage,
          remaining: limit === -1 ? -1 : Math.max(0, limit - usage),
          percentage: Math.min(100, percentage)
        }
        
        // Add warnings for high usage
        if (percentage >= 80 && limit !== -1) {
          summary.warnings.push({
            feature,
            message: `You're using ${Math.round(percentage)}% of your ${feature} limit`,
            severity: percentage >= 95 ? 'high' : 'medium'
          })
        }
        
        // Add upgrade recommendations
        if (percentage >= 90 && limit !== -1) {
          summary.upgradeRecommendations.push({
            feature,
            currentPlan: subscription.plan,
            recommendedPlans: this.getRecommendedPlans(feature, subscription.plan),
            prompt: this.upgradePrompts[feature]
          })
        }
      }
    }

    return summary
  }

  getRecommendedPlans(feature, currentPlan) {
    const planHierarchy = ['free', 'team', 'business', 'enterprise']
    const currentIndex = planHierarchy.indexOf(currentPlan)
    
    return planHierarchy.slice(currentIndex + 1)
  }

  async shouldShowUpgradePrompt(userId, feature, context = {}) {
    const subscription = await this.getUserSubscription(userId)
    
    // Don't show prompts to paying customers for features they have
    if (subscription.plan !== 'free' && subscription.hasFeature(feature)) {
      return false
    }
    
    // Check usage-based prompts
    if (this.features[feature]?.type === 'limit') {
      const percentage = subscription.getUsagePercentage(feature)
      const threshold = context.threshold || 80
      
      if (percentage >= threshold) {
        return {
          show: true,
          prompt: this.upgradePrompts[feature],
          usage: {
            percentage,
            limit: subscription.getFeatureLimit(feature),
            current: subscription.usage[feature] || 0
          },
          recommendedPlans: this.getRecommendedPlans(feature, subscription.plan)
        }
      }
    }
    
    // Check feature access prompts
    if (this.features[feature]?.type === 'boolean' && !subscription.hasFeature(feature)) {
      return {
        show: true,
        prompt: this.upgradePrompts[feature],
        recommendedPlans: this.getRecommendedPlans(feature, subscription.plan)
      }
    }
    
    return { show: false }
  }

  async getUpgradeOptions(userId, targetFeature = null) {
    const subscription = await this.getUserSubscription(userId)
    const availablePlans = subscription.canUpgrade()
    
    const options = availablePlans.map(plan => {
      const pricing = this.getPlanPricing(plan)
      const features = this.getPlanFeatures(plan)
      
      return {
        plan,
        pricing,
        features,
        savings: this.calculateSavings(pricing),
        popular: plan === 'team', // Mark team as popular
        recommended: this.isRecommendedForFeature(plan, targetFeature)
      }
    })
    
    return options
  }

  getPlanPricing(plan) {
    const pricing = {
      team: { monthly: 12, yearly: 120, yearlyDiscount: 17 },
      business: { monthly: 25, yearly: 250, yearlyDiscount: 17 },
      enterprise: { monthly: 50, yearly: 500, yearlyDiscount: 17 }
    }
    
    return pricing[plan] || { monthly: 0, yearly: 0, yearlyDiscount: 0 }
  }

  getPlanFeatures(plan) {
    const subscription = new Subscription({ plan })
    subscription.setFeaturesByPlan()
    return subscription.features
  }

  calculateSavings(pricing) {
    if (!pricing.yearly || !pricing.monthly) return 0
    const yearlyEquivalent = pricing.monthly * 12
    return Math.round(((yearlyEquivalent - pricing.yearly) / yearlyEquivalent) * 100)
  }

  isRecommendedForFeature(plan, feature) {
    if (!feature) return false
    
    const recommendations = {
      'teamMembers': 'team',
      'teamCollaboration': 'team',
      'advancedAnalytics': 'team',
      'whiteLabel': 'business',
      'sso': 'business',
      'auditLogs': 'business'
    }
    
    return recommendations[feature] === plan
  }
}

class FeatureRestrictedError extends Error {
  constructor(feature, prompt, usage = null) {
    super(`Feature '${feature}' is restricted`)
    this.name = 'FeatureRestrictedError'
    this.feature = feature
    this.prompt = prompt
    this.usage = usage
  }
}

const featureGateService = new FeatureGateService()

module.exports = {
  FeatureGateService,
  FeatureRestrictedError,
  featureGateService
}
