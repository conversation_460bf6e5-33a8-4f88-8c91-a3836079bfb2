/**
 * AI-Powered Features Routes
 * Handles AI-driven productivity features and machine learning operations
 */

const express = require('express');
const aiController = require('../controllers/aiController');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// Task prioritization and scheduling
router.post('/prioritize-tasks', aiController.prioritizeTasks);
router.post('/schedule-tasks', aiController.scheduleTasks);
router.post('/optimize-schedule', aiController.optimizeSchedule);

// Productivity coaching
router.get('/coaching/insights', aiController.getCoachingInsights);
router.get('/coaching/recommendations', aiController.getCoachingRecommendations);
router.post('/coaching/feedback', aiController.submitCoachingFeedback);

// Pattern analysis
router.get('/patterns/productivity', aiController.analyzeProductivityPatterns);
router.get('/patterns/behavior', aiController.analyzeBehaviorPatterns);
router.get('/patterns/energy', aiController.analyzeEnergyPatterns);

// Predictive analytics
router.get('/predictions/productivity', aiController.predictProductivity);
router.get('/predictions/optimal-times', aiController.predictOptimalTimes);
router.get('/predictions/task-duration', aiController.predictTaskDuration);

// Smart suggestions
router.get('/suggestions/tasks', aiController.getTaskSuggestions);
router.get('/suggestions/breaks', aiController.getBreakSuggestions);
router.get('/suggestions/focus-times', aiController.getFocusTimeSuggestions);

// Habit formation
router.get('/habits/analysis', aiController.analyzeHabits);
router.post('/habits/recommendations', aiController.getHabitRecommendations);
router.post('/habits/track', aiController.trackHabitProgress);

// AI model management
router.get('/models/status', aiController.getModelStatus);
router.post('/models/retrain', aiController.retrainModels);
router.get('/models/performance', aiController.getModelPerformance);

module.exports = router;
