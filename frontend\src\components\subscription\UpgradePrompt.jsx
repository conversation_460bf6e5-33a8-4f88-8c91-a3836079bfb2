import React, { useState } from 'react'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { XMarkIcon, SparklesIcon, ArrowRightIcon } from '@heroicons/react/24/outline'
import { motion, AnimatePresence } from 'framer-motion'
import PricingPlans from './PricingPlans'

const UpgradePrompt = ({ 
  feature, 
  title, 
  description, 
  benefits = [], 
  onClose, 
  onUpgrade,
  showPlans = false,
  size = 'modal' // 'modal', 'banner', 'inline'
}) => {
  const { subscription, hasFeature, getUsagePercentage } = useSubscription()
  const [showPricingPlans, setShowPricingPlans] = useState(showPlans)

  const handleUpgradeClick = () => {
    if (onUpgrade) {
      onUpgrade()
    } else {
      setShowPricingPlans(true)
    }
  }

  const getFeatureTitle = () => {
    if (title) return title
    
    const featureTitles = {
      projects: 'More Projects',
      teamMembers: 'Team Collaboration',
      workspaces: 'Multiple Workspaces',
      advancedAnalytics: 'Advanced Analytics',
      exportData: 'Data Export',
      apiAccess: 'API Access',
      customIntegrations: 'Custom Integrations'
    }
    
    return featureTitles[feature] || 'Premium Feature'
  }

  const getFeatureDescription = () => {
    if (description) return description
    
    const descriptions = {
      projects: 'Create unlimited projects and organize your work better',
      teamMembers: 'Collaborate with your team and boost productivity together',
      workspaces: 'Organize different areas of your work with multiple workspaces',
      advancedAnalytics: 'Get detailed insights into your productivity patterns',
      exportData: 'Export your data in multiple formats for analysis',
      apiAccess: 'Integrate TimeWarp with your favorite tools via API',
      customIntegrations: 'Build custom integrations tailored to your workflow'
    }
    
    return descriptions[feature] || 'Unlock this premium feature to enhance your productivity'
  }

  const getFeatureBenefits = () => {
    if (benefits.length > 0) return benefits
    
    const featureBenefits = {
      projects: [
        'Unlimited project creation',
        'Advanced project templates',
        'Project analytics and insights',
        'Team project sharing'
      ],
      teamMembers: [
        'Invite unlimited team members',
        'Real-time collaboration',
        'Team performance analytics',
        'Shared workspaces and goals'
      ],
      workspaces: [
        'Create multiple workspaces',
        'Organize by client or department',
        'Workspace-specific settings',
        'Cross-workspace reporting'
      ],
      advancedAnalytics: [
        'Detailed productivity reports',
        'Time tracking insights',
        'Goal achievement analytics',
        'Custom dashboard widgets'
      ]
    }
    
    return featureBenefits[feature] || [
      'Enhanced productivity features',
      'Priority customer support',
      'Advanced customization options',
      'Regular feature updates'
    ]
  }

  if (size === 'banner') {
    return (
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4"
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <SparklesIcon className="w-6 h-6" />
            <div>
              <h3 className="font-semibold">{getFeatureTitle()}</h3>
              <p className="text-sm opacity-90">{getFeatureDescription()}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleUpgradeClick}
              className="bg-white text-blue-600 px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Upgrade Now
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </motion.div>
    )
  }

  if (size === 'inline') {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <SparklesIcon className="w-5 h-5 text-blue-500 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-semibold text-blue-900">{getFeatureTitle()}</h4>
            <p className="text-sm text-blue-700 mt-1">{getFeatureDescription()}</p>
            <button
              onClick={handleUpgradeClick}
              className="mt-2 text-sm font-medium text-blue-600 hover:text-blue-800 flex items-center"
            >
              Upgrade to unlock
              <ArrowRightIcon className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Modal size (default)
  return (
    <AnimatePresence>
      {!showPricingPlans ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-white rounded-2xl max-w-md w-full p-6"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <SparklesIcon className="w-6 h-6 text-blue-500" />
                <h2 className="text-xl font-bold text-gray-900">Upgrade Required</h2>
              </div>
              {onClose && (
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              )}
            </div>

            {/* Content */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {getFeatureTitle()}
              </h3>
              <p className="text-gray-600 mb-4">
                {getFeatureDescription()}
              </p>

              {/* Benefits */}
              <div className="space-y-2">
                {getFeatureBenefits().map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Current Plan Info */}
            {subscription && (
              <div className="bg-gray-50 rounded-lg p-3 mb-6">
                <div className="text-sm text-gray-600">
                  Current plan: <span className="font-semibold capitalize">{subscription.plan}</span>
                </div>
                {feature && getUsagePercentage(feature) > 0 && (
                  <div className="mt-2">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Usage</span>
                      <span>{Math.round(getUsagePercentage(feature))}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getUsagePercentage(feature)}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-3">
              <button
                onClick={handleUpgradeClick}
                className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                View Plans
              </button>
              {onClose && (
                <button
                  onClick={onClose}
                  className="px-4 py-3 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Maybe Later
                </button>
              )}
            </div>

            {/* Trial Notice */}
            <p className="text-xs text-gray-500 text-center mt-3">
              Start with a 14-day free trial • Cancel anytime
            </p>
          </motion.div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-white z-50 overflow-y-auto"
        >
          <div className="min-h-screen">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
              <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
                <h1 className="text-2xl font-bold text-gray-900">Choose Your Plan</h1>
                <button
                  onClick={onClose || (() => setShowPricingPlans(false))}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Pricing Plans */}
            <PricingPlans
              targetFeature={feature}
              onPlanSelect={(plan, cycle) => {
                if (onUpgrade) {
                  onUpgrade(plan, cycle)
                }
                if (onClose) {
                  onClose()
                }
              }}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default UpgradePrompt
