const { TeamActivity, TeamMember, User } = require('../models')
const { Op } = require('sequelize')

// Get team activity feed
exports.getTeamActivities = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    const { 
      type, 
      userId: filterUserId, 
      entityType, 
      priority,
      page = 1, 
      limit = 50,
      since
    } = req.query
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    // Build where clause
    const where = { 
      teamId,
      visibility: ['public', 'team']
    }
    
    if (type) where.type = type
    if (filterUserId) where.userId = filterUserId
    if (entityType) where.entityType = entityType
    if (priority) where.priority = priority
    if (since) where.createdAt = { [Op.gte]: new Date(since) }
    
    const offset = (page - 1) * limit
    
    const { count, rows: activities } = await TeamActivity.findAndCountAll({
      where,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar']
      }],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    })
    
    // Format activities with relative time
    const formattedActivities = activities.map(activity => ({
      ...activity.toJSON(),
      formattedTime: activity.getFormattedTime(),
      isRead: activity.readBy.includes(userId)
    }))
    
    res.json({
      success: true,
      data: {
        activities: formattedActivities,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get team activities error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch team activities',
      error: error.message
    })
  }
}

// Mark activities as read
exports.markActivitiesAsRead = async (req, res) => {
  try {
    const { teamId } = req.params
    const { activityIds } = req.body
    const userId = req.user.id
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    // Update activities
    const activities = await TeamActivity.findAll({
      where: {
        id: activityIds,
        teamId
      }
    })
    
    const updatePromises = activities.map(activity => activity.markAsRead(userId))
    await Promise.all(updatePromises)
    
    res.json({
      success: true,
      message: `${activities.length} activities marked as read`
    })
  } catch (error) {
    console.error('Mark activities as read error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to mark activities as read',
      error: error.message
    })
  }
}

// Get activity statistics
exports.getActivityStats = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    const { period = '7d' } = req.query
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    // Calculate date range
    const end = new Date()
    let start
    
    switch (period) {
      case '24h':
        start = new Date(end.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000)
    }
    
    // Get activities in period
    const activities = await TeamActivity.findAll({
      where: {
        teamId,
        createdAt: { [Op.between]: [start, end] }
      },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar']
      }]
    })
    
    // Calculate statistics
    const stats = {
      total: activities.length,
      byType: {},
      byUser: {},
      byPriority: {
        high: 0,
        medium: 0,
        low: 0
      },
      byHour: Array(24).fill(0),
      trend: []
    }
    
    // Group by type
    activities.forEach(activity => {
      stats.byType[activity.type] = (stats.byType[activity.type] || 0) + 1
      stats.byPriority[activity.priority]++
      
      // Group by user
      const userName = activity.user.name
      if (!stats.byUser[userName]) {
        stats.byUser[userName] = {
          count: 0,
          user: activity.user
        }
      }
      stats.byUser[userName].count++
      
      // Group by hour
      const hour = new Date(activity.createdAt).getHours()
      stats.byHour[hour]++
    })
    
    // Calculate daily trend
    const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24))
    for (let i = 0; i < days; i++) {
      const date = new Date(start.getTime() + i * 24 * 60 * 60 * 1000)
      const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000)
      
      const dayActivities = activities.filter(a => {
        const activityDate = new Date(a.createdAt)
        return activityDate >= date && activityDate < nextDate
      })
      
      stats.trend.push({
        date: date.toISOString().split('T')[0],
        count: dayActivities.length
      })
    }
    
    // Convert byUser object to array and sort
    stats.byUser = Object.values(stats.byUser).sort((a, b) => b.count - a.count)
    
    res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get activity stats error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch activity statistics',
      error: error.message
    })
  }
}

// Get real-time activity updates
exports.getRealtimeUpdates = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    const { lastActivityId } = req.query
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    // Build where clause for new activities
    const where = {
      teamId,
      visibility: ['public', 'team']
    }
    
    if (lastActivityId) {
      // Get activities newer than the last known activity
      const lastActivity = await TeamActivity.findByPk(lastActivityId)
      if (lastActivity) {
        where.createdAt = { [Op.gt]: lastActivity.createdAt }
      }
    } else {
      // Get activities from last 5 minutes if no lastActivityId
      where.createdAt = { [Op.gte]: new Date(Date.now() - 5 * 60 * 1000) }
    }
    
    const activities = await TeamActivity.findAll({
      where,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar']
      }],
      order: [['createdAt', 'DESC']],
      limit: 50
    })
    
    // Format activities
    const formattedActivities = activities.map(activity => ({
      ...activity.toJSON(),
      formattedTime: activity.getFormattedTime(),
      isRead: activity.readBy.includes(userId)
    }))
    
    res.json({
      success: true,
      data: {
        activities: formattedActivities,
        hasNewActivities: activities.length > 0,
        lastActivityId: activities.length > 0 ? activities[0].id : lastActivityId
      }
    })
  } catch (error) {
    console.error('Get realtime updates error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch realtime updates',
      error: error.message
    })
  }
}

module.exports = {
  getTeamActivities: exports.getTeamActivities,
  markActivitiesAsRead: exports.markActivitiesAsRead,
  getActivityStats: exports.getActivityStats,
  getRealtimeUpdates: exports.getRealtimeUpdates
}
