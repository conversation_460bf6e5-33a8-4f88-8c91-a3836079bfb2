const { TeamGoal, Team, TeamMember, User, TeamActivity } = require('../models')
const { Op } = require('sequelize')
const { validationResult } = require('express-validator')

// Get team goals
exports.getTeamGoals = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    const { status, category, assignedTo, page = 1, limit = 20 } = req.query
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    // Build where clause
    const where = { teamId }
    if (status) where.status = status
    if (category) where.category = category
    if (assignedTo) where.assignedToId = assignedTo
    
    const offset = (page - 1) * limit
    
    const { count, rows: goals } = await TeamGoal.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'name', 'email', 'avatar'],
          required: false
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ],
      order: [
        ['priority', 'DESC'],
        ['endDate', 'ASC'],
        ['createdAt', 'DESC']
      ],
      limit: parseInt(limit),
      offset
    })
    
    // Add progress data to each goal
    const goalsWithProgress = goals.map(goal => ({
      ...goal.toJSON(),
      progress: goal.getProgress(),
      timelineData: goal.getTimelineData()
    }))
    
    res.json({
      success: true,
      data: {
        goals: goalsWithProgress,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get team goals error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch team goals',
      error: error.message
    })
  }
}

// Create team goal
exports.createTeamGoal = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }
    
    const { teamId } = req.params
    const userId = req.user.id
    const {
      title,
      description,
      type,
      category,
      priority,
      targetValue,
      unit,
      assignedToId,
      startDate,
      endDate,
      tags,
      linkedProjectIds,
      linkedTaskIds
    } = req.body
    
    // Check if user has permission to create goals
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member || !member.hasPermission('canCreateGoals')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to create goals.'
      })
    }
    
    // Validate assigned user is team member
    if (assignedToId) {
      const assignedMember = await TeamMember.findOne({
        where: { teamId, userId: assignedToId, isActive: true }
      })
      
      if (!assignedMember) {
        return res.status(400).json({
          success: false,
          message: 'Assigned user is not a member of this team'
        })
      }
    }
    
    const goal = await TeamGoal.create({
      teamId,
      title,
      description,
      type,
      category,
      priority,
      targetValue,
      unit,
      assignedToId,
      createdById: userId,
      startDate,
      endDate,
      tags: tags || [],
      linkedProjectIds: linkedProjectIds || [],
      linkedTaskIds: linkedTaskIds || [],
      status: 'active'
    })
    
    // Load goal with associations
    const createdGoal = await TeamGoal.findByPk(goal.id, {
      include: [
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'name', 'email', 'avatar']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    })
    
    // Log activity
    await TeamActivity.createGoalActivity(teamId, userId, 'goal_created', createdGoal)
    
    res.status(201).json({
      success: true,
      data: {
        ...createdGoal.toJSON(),
        progress: createdGoal.getProgress()
      },
      message: 'Goal created successfully'
    })
  } catch (error) {
    console.error('Create team goal error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create goal',
      error: error.message
    })
  }
}

// Update goal progress
exports.updateGoalProgress = async (req, res) => {
  try {
    const { teamId, goalId } = req.params
    const { value, increment = false } = req.body
    const userId = req.user.id
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    const goal = await TeamGoal.findOne({
      where: { id: goalId, teamId }
    })
    
    if (!goal) {
      return res.status(404).json({
        success: false,
        message: 'Goal not found'
      })
    }
    
    // Check if user can update this goal
    if (goal.assignedToId && goal.assignedToId !== userId && !member.hasPermission('canCreateGoals')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only update your assigned goals.'
      })
    }
    
    // Update progress
    const newValue = increment ? goal.currentValue + value : value
    const progress = await goal.updateProgress(newValue, userId)
    
    res.json({
      success: true,
      data: {
        goalId: goal.id,
        currentValue: goal.currentValue,
        progress
      },
      message: 'Goal progress updated successfully'
    })
  } catch (error) {
    console.error('Update goal progress error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update goal progress',
      error: error.message
    })
  }
}

// Get goal details
exports.getGoalDetails = async (req, res) => {
  try {
    const { teamId, goalId } = req.params
    const userId = req.user.id
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    const goal = await TeamGoal.findOne({
      where: { id: goalId, teamId },
      include: [
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'name', 'email', 'avatar']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    })
    
    if (!goal) {
      return res.status(404).json({
        success: false,
        message: 'Goal not found'
      })
    }
    
    // Get related activities
    const activities = await TeamActivity.findAll({
      where: {
        teamId,
        entityType: 'goal',
        entityId: goalId
      },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar']
      }],
      order: [['createdAt', 'DESC']],
      limit: 20
    })
    
    res.json({
      success: true,
      data: {
        ...goal.toJSON(),
        progress: goal.getProgress(),
        timelineData: goal.getTimelineData(),
        activities: activities.map(activity => ({
          ...activity.toJSON(),
          formattedTime: activity.getFormattedTime()
        }))
      }
    })
  } catch (error) {
    console.error('Get goal details error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch goal details',
      error: error.message
    })
  }
}

// Delete goal
exports.deleteGoal = async (req, res) => {
  try {
    const { teamId, goalId } = req.params
    const userId = req.user.id
    
    // Check if user has permission to delete goals
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member || !member.hasPermission('canCreateGoals')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to delete goals.'
      })
    }
    
    const goal = await TeamGoal.findOne({
      where: { id: goalId, teamId }
    })
    
    if (!goal) {
      return res.status(404).json({
        success: false,
        message: 'Goal not found'
      })
    }
    
    await goal.destroy()
    
    // Log activity
    await TeamActivity.create({
      teamId,
      userId,
      type: 'goal_deleted',
      description: `deleted goal "${goal.title}"`,
      entityType: 'goal',
      metadata: {
        goalTitle: goal.title,
        goalId: goalId
      }
    })
    
    res.json({
      success: true,
      message: 'Goal deleted successfully'
    })
  } catch (error) {
    console.error('Delete goal error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete goal',
      error: error.message
    })
  }
}

module.exports = {
  getTeamGoals: exports.getTeamGoals,
  createTeamGoal: exports.createTeamGoal,
  updateGoalProgress: exports.updateGoalProgress,
  getGoalDetails: exports.getGoalDetails,
  deleteGoal: exports.deleteGoal
}
