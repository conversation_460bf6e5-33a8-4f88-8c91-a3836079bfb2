/**
 * Focus Controller
 * Handles focus mode sessions, website/app blocking, and distraction management
 */

const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const FocusSession = require('../models/FocusSession');
const { Op } = require('sequelize');
const moment = require('moment');

/**
 * Get user's focus sessions with filtering and pagination
 */
const getFocusSessions = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      startDate,
      endDate,
      type,
      completionStatus,
      isActive
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { userId: req.user.id };

    // Apply filters
    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime[Op.gte] = new Date(startDate);
      if (endDate) where.startTime[Op.lte] = new Date(endDate);
    }

    if (type) where.type = type;
    if (completionStatus) where.completionStatus = completionStatus;
    if (isActive !== undefined) where.isActive = isActive === 'true';

    const { count, rows: sessions } = await FocusSession.findAndCountAll({
      where,
      order: [['startTime', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    res.status(200).json({
      status: 'success',
      results: sessions.length,
      totalCount: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      data: { sessions }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new focus session
 */
const createFocusSession = async (req, res, next) => {
  try {
    const {
      name,
      type = 'custom',
      duration = 25,
      blockingEnabled = true,
      blockedWebsites = [],
      blockedApplications = [],
      allowedWebsites = [],
      allowedApplications = [],
      blockingMode = 'blacklist',
      goals = [],
      settings = {},
      pomodoroConfig = {}
    } = req.body;

    // Check if user has an active focus session
    const activeSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      }
    });

    if (activeSession) {
      return next(new AppError('You already have an active focus session. Please end it first.', 400));
    }

    const session = await FocusSession.create({
      userId: req.user.id,
      name,
      type,
      duration,
      blockingEnabled,
      blockedWebsites,
      blockedApplications,
      allowedWebsites,
      allowedApplications,
      blockingMode,
      goals,
      settings: {
        strictMode: false,
        allowBreaks: true,
        breakDuration: 5,
        notifications: true,
        soundEnabled: true,
        backgroundMusic: false,
        ...settings
      },
      pomodoroConfig: {
        workDuration: 25,
        shortBreak: 5,
        longBreak: 15,
        cyclesBeforeLongBreak: 4,
        currentCycle: 1,
        ...pomodoroConfig
      }
    });

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('focus-session-started', {
        sessionId: session.id,
        type: session.type,
        duration: session.duration,
        blockingEnabled: session.blockingEnabled
      });
    }

    logger.logUserAction('focus_session_started', req.user.id, {
      sessionId: session.id,
      type,
      duration
    });

    res.status(201).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get a specific focus session
 */
const getFocusSession = async (req, res, next) => {
  try {
    const session = await FocusSession.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });

    if (!session) {
      return next(new AppError('Focus session not found', 404));
    }

    res.status(200).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update a focus session
 */
const updateFocusSession = async (req, res, next) => {
  try {
    const session = await FocusSession.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });

    if (!session) {
      return next(new AppError('Focus session not found', 404));
    }

    // Don't allow updating core timing data for active sessions
    if (session.isActive) {
      const allowedFields = ['name', 'goals', 'notes', 'tags', 'settings'];
      const updateData = {};

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      });

      await session.update(updateData);
    } else {
      // Allow full updates for completed sessions
      await session.update(req.body);
    }

    res.status(200).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a focus session
 */
const deleteFocusSession = async (req, res, next) => {
  try {
    const session = await FocusSession.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });

    if (!session) {
      return next(new AppError('Focus session not found', 404));
    }

    await session.destroy();

    logger.logUserAction('focus_session_deleted', req.user.id, {
      sessionId: session.id,
      duration: session.actualDuration
    });

    res.status(204).json({
      status: 'success',
      data: null
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current active focus session
 */
const getActiveFocusSession = async (req, res, next) => {
  try {
    const activeSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      }
    });

    res.status(200).json({
      status: 'success',
      data: { session: activeSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Start focus session (alias for createFocusSession)
 */
const startFocusSession = async (req, res, next) => {
  return createFocusSession(req, res, next);
};

/**
 * Stop active focus session
 */
const stopFocusSession = async (req, res, next) => {
  try {
    const activeSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      }
    });

    if (!activeSession) {
      return next(new AppError('No active focus session found', 404));
    }

    await activeSession.complete();

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('focus-session-stopped', {
        sessionId: activeSession.id,
        endTime: activeSession.endTime,
        actualDuration: activeSession.actualDuration,
        focusScore: activeSession.focusScore
      });
    }

    logger.logUserAction('focus_session_completed', req.user.id, {
      sessionId: activeSession.id,
      actualDuration: activeSession.actualDuration,
      focusScore: activeSession.focusScore
    });

    res.status(200).json({
      status: 'success',
      data: { session: activeSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Pause active focus session
 */
const pauseFocusSession = async (req, res, next) => {
  try {
    const activeSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true,
        isPaused: false
      }
    });

    if (!activeSession) {
      return next(new AppError('No active focus session found', 404));
    }

    await activeSession.pause();

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('focus-session-paused', {
        sessionId: activeSession.id
      });
    }

    res.status(200).json({
      status: 'success',
      data: { session: activeSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Resume paused focus session
 */
const resumeFocusSession = async (req, res, next) => {
  try {
    const pausedSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true,
        isPaused: true
      }
    });

    if (!pausedSession) {
      return next(new AppError('No paused focus session found', 404));
    }

    await pausedSession.resume();

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('focus-session-resumed', {
        sessionId: pausedSession.id
      });
    }

    res.status(200).json({
      status: 'success',
      data: { session: pausedSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user's blocking lists and rules
 */
const getBlocklists = async (req, res, next) => {
  try {
    // Get default blocking rules from user preferences or recent sessions
    const recentSessions = await FocusSession.findAll({
      where: {
        userId: req.user.id,
        blockingEnabled: true
      },
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    // Aggregate commonly blocked sites and apps
    const websiteFrequency = {};
    const appFrequency = {};

    recentSessions.forEach(session => {
      session.blockedWebsites.forEach(site => {
        websiteFrequency[site] = (websiteFrequency[site] || 0) + 1;
      });
      session.blockedApplications.forEach(app => {
        appFrequency[app] = (appFrequency[app] || 0) + 1;
      });
    });

    const commonBlockedWebsites = Object.entries(websiteFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([site, count]) => ({ site, frequency: count }));

    const commonBlockedApps = Object.entries(appFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([app, count]) => ({ app, frequency: count }));

    // Default productivity-focused blocklists
    const defaultBlocklists = {
      social_media: {
        name: 'Social Media',
        websites: ['facebook.com', 'twitter.com', 'instagram.com', 'tiktok.com', 'linkedin.com'],
        applications: ['Facebook', 'Twitter', 'Instagram', 'TikTok'],
        category: 'social'
      },
      entertainment: {
        name: 'Entertainment',
        websites: ['youtube.com', 'netflix.com', 'twitch.tv', 'reddit.com', 'imgur.com'],
        applications: ['Netflix', 'YouTube', 'Spotify', 'Steam'],
        category: 'entertainment'
      },
      news: {
        name: 'News & Media',
        websites: ['cnn.com', 'bbc.com', 'reddit.com', 'news.ycombinator.com'],
        applications: ['News', 'Apple News'],
        category: 'news'
      },
      shopping: {
        name: 'Shopping',
        websites: ['amazon.com', 'ebay.com', 'etsy.com', 'alibaba.com'],
        applications: ['Amazon', 'eBay'],
        category: 'shopping'
      }
    };

    res.status(200).json({
      status: 'success',
      data: {
        defaultBlocklists,
        commonBlockedWebsites,
        commonBlockedApps,
        recentSessionsCount: recentSessions.length
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create custom blocklist
 */
const createBlocklist = async (req, res, next) => {
  try {
    const { name, websites = [], applications = [], category = 'custom' } = req.body;

    if (!name) {
      return next(new AppError('Blocklist name is required', 400));
    }

    // Store as user preference (this would typically be in a UserPreferences model)
    const blocklist = {
      id: Date.now().toString(),
      name,
      websites,
      applications,
      category,
      createdAt: new Date(),
      userId: req.user.id
    };

    // For now, return the created blocklist
    // In a real implementation, this would be saved to a UserBlocklists table
    res.status(201).json({
      status: 'success',
      data: { blocklist }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update blocklist
 */
const updateBlocklist = async (req, res, next) => {
  try {
    // Implementation would update the blocklist in database
    res.status(200).json({
      status: 'success',
      message: 'Blocklist updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete blocklist
 */
const deleteBlocklist = async (req, res, next) => {
  try {
    // Implementation would delete the blocklist from database
    res.status(204).json({
      status: 'success',
      data: null
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get Pomodoro settings
 */
const getPomodoroSettings = async (req, res, next) => {
  try {
    // Get user's Pomodoro preferences from recent sessions or defaults
    const recentPomodoroSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        type: 'pomodoro'
      },
      order: [['createdAt', 'DESC']]
    });

    const defaultSettings = {
      workDuration: 25,
      shortBreak: 5,
      longBreak: 15,
      cyclesBeforeLongBreak: 4,
      autoStartBreaks: false,
      autoStartWork: false,
      notifications: true,
      soundEnabled: true,
      tickingSound: false
    };

    const settings = recentPomodoroSession?.pomodoroConfig || defaultSettings;

    res.status(200).json({
      status: 'success',
      data: { settings }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update Pomodoro settings
 */
const updatePomodoroSettings = async (req, res, next) => {
  try {
    const settings = req.body;

    // Validate settings
    if (settings.workDuration && (settings.workDuration < 1 || settings.workDuration > 120)) {
      return next(new AppError('Work duration must be between 1 and 120 minutes', 400));
    }

    // In a real implementation, save to user preferences
    res.status(200).json({
      status: 'success',
      data: { settings },
      message: 'Pomodoro settings updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Start Pomodoro session
 */
const startPomodoro = async (req, res, next) => {
  try {
    const { workDuration = 25, shortBreak = 5, longBreak = 15, cyclesBeforeLongBreak = 4 } = req.body;

    // Check for active session
    const activeSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      }
    });

    if (activeSession) {
      return next(new AppError('You already have an active focus session. Please end it first.', 400));
    }

    const session = await FocusSession.create({
      userId: req.user.id,
      name: 'Pomodoro Work Session',
      type: 'pomodoro',
      duration: workDuration,
      blockingEnabled: true,
      pomodoroConfig: {
        workDuration,
        shortBreak,
        longBreak,
        cyclesBeforeLongBreak,
        currentCycle: 1
      },
      // Default Pomodoro blocking rules
      blockedWebsites: ['facebook.com', 'twitter.com', 'instagram.com', 'youtube.com', 'reddit.com'],
      blockingMode: 'blacklist'
    });

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('pomodoro-started', {
        sessionId: session.id,
        duration: workDuration,
        cycle: 1
      });
    }

    res.status(201).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Start Pomodoro break
 */
const startPomodoroBreak = async (req, res, next) => {
  try {
    const { duration = 5, isLongBreak = false } = req.body;

    const session = await FocusSession.create({
      userId: req.user.id,
      name: isLongBreak ? 'Pomodoro Long Break' : 'Pomodoro Short Break',
      type: 'break',
      duration,
      blockingEnabled: false, // No blocking during breaks
      pomodoroConfig: {
        isBreak: true,
        isLongBreak
      }
    });

    res.status(201).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get distraction records
 */
const getDistractions = async (req, res, next) => {
  try {
    const { startDate, endDate, limit = 50 } = req.query;
    const where = { userId: req.user.id };

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime[Op.gte] = new Date(startDate);
      if (endDate) where.startTime[Op.lte] = new Date(endDate);
    }

    const sessions = await FocusSession.findAll({
      where,
      attributes: ['id', 'startTime', 'endTime', 'distractions', 'distractionCount'],
      order: [['startTime', 'DESC']],
      limit: parseInt(limit)
    });

    // Flatten all distractions
    const allDistractions = [];
    sessions.forEach(session => {
      if (session.distractions && session.distractions.length > 0) {
        session.distractions.forEach(distraction => {
          allDistractions.push({
            ...distraction,
            sessionId: session.id
          });
        });
      }
    });

    res.status(200).json({
      status: 'success',
      results: allDistractions.length,
      data: { distractions: allDistractions }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Record a distraction event
 */
const recordDistraction = async (req, res, next) => {
  try {
    const { type, source, duration = 0, blocked = false, metadata = {} } = req.body;

    const activeSession = await FocusSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      }
    });

    if (!activeSession) {
      return next(new AppError('No active focus session found', 404));
    }

    await activeSession.addDistraction({
      type,
      source,
      duration,
      blocked,
      metadata
    });

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('distraction-recorded', {
        sessionId: activeSession.id,
        type,
        source,
        blocked
      });
    }

    res.status(201).json({
      status: 'success',
      data: {
        sessionId: activeSession.id,
        distractionCount: activeSession.distractionCount
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get distraction analysis and insights
 */
const getDistractionAnalysis = async (req, res, next) => {
  try {
    const { days = 30 } = req.query;
    const startDate = moment().subtract(days, 'days').toDate();

    const sessions = await FocusSession.findAll({
      where: {
        userId: req.user.id,
        startTime: { [Op.gte]: startDate },
        endTime: { [Op.not]: null }
      }
    });

    // Analyze distraction patterns
    const distractionsByType = {};
    const distractionsBySource = {};
    const distractionsByHour = Array.from({ length: 24 }, () => 0);
    let totalDistractions = 0;
    let totalBlockedDistractions = 0;

    sessions.forEach(session => {
      if (session.distractions) {
        session.distractions.forEach(distraction => {
          totalDistractions++;

          // Count by type
          distractionsByType[distraction.type] = (distractionsByType[distraction.type] || 0) + 1;

          // Count by source
          if (distraction.source) {
            distractionsBySource[distraction.source] = (distractionsBySource[distraction.source] || 0) + 1;
          }

          // Count by hour
          const hour = moment(distraction.timestamp).hour();
          distractionsByHour[hour]++;

          // Count blocked distractions
          if (distraction.blocked) {
            totalBlockedDistractions++;
          }
        });
      }
    });

    // Calculate averages
    const totalSessions = sessions.length;
    const averageDistractions = totalSessions > 0 ? totalDistractions / totalSessions : 0;
    const blockingEffectiveness = totalDistractions > 0 ? (totalBlockedDistractions / totalDistractions) * 100 : 0;

    // Find peak distraction hours
    const peakDistractionHours = distractionsByHour
      .map((count, hour) => ({ hour, count }))
      .filter(data => data.count > 0)
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(data => data.hour);

    // Top distraction sources
    const topDistractionSources = Object.entries(distractionsBySource)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([source, count]) => ({ source, count }));

    res.status(200).json({
      status: 'success',
      data: {
        totalDistractions,
        totalBlockedDistractions,
        averageDistractions: Math.round(averageDistractions * 100) / 100,
        blockingEffectiveness: Math.round(blockingEffectiveness * 100) / 100,
        distractionsByType,
        topDistractionSources,
        peakDistractionHours,
        distractionsByHour,
        analysisPeriod: `${days} days`,
        totalSessions
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get focus insights and productivity metrics
 */
const getFocusInsights = async (req, res, next) => {
  try {
    const { days = 30 } = req.query;
    const startDate = moment().subtract(days, 'days').toDate();

    const sessions = await FocusSession.findAll({
      where: {
        userId: req.user.id,
        startTime: { [Op.gte]: startDate }
      }
    });

    // Calculate insights
    const totalSessions = sessions.length;
    const completedSessions = sessions.filter(s => s.completionStatus === 'completed').length;
    const totalPlannedTime = sessions.reduce((sum, s) => sum + s.duration, 0);
    const totalActualTime = sessions.reduce((sum, s) => sum + (s.actualDuration || 0), 0);

    const completionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0;
    const averageFocusScore = sessions.length > 0
      ? sessions.reduce((sum, s) => sum + (s.focusScore || 0), 0) / sessions.length
      : 0;

    // Analyze by session type
    const sessionsByType = {};
    sessions.forEach(session => {
      if (!sessionsByType[session.type]) {
        sessionsByType[session.type] = {
          count: 0,
          totalTime: 0,
          completedCount: 0,
          averageFocusScore: 0
        };
      }

      sessionsByType[session.type].count++;
      sessionsByType[session.type].totalTime += session.actualDuration || 0;
      if (session.completionStatus === 'completed') {
        sessionsByType[session.type].completedCount++;
      }
      sessionsByType[session.type].averageFocusScore += session.focusScore || 0;
    });

    // Calculate averages for each type
    Object.keys(sessionsByType).forEach(type => {
      const typeData = sessionsByType[type];
      typeData.averageFocusScore = typeData.count > 0 ? typeData.averageFocusScore / typeData.count : 0;
      typeData.completionRate = typeData.count > 0 ? (typeData.completedCount / typeData.count) * 100 : 0;
    });

    // Find best performing hours
    const hourlyPerformance = Array.from({ length: 24 }, () => ({ sessions: 0, totalScore: 0 }));
    sessions.forEach(session => {
      const hour = moment(session.startTime).hour();
      hourlyPerformance[hour].sessions++;
      hourlyPerformance[hour].totalScore += session.focusScore || 0;
    });

    const bestHours = hourlyPerformance
      .map((data, hour) => ({
        hour,
        averageScore: data.sessions > 0 ? data.totalScore / data.sessions : 0,
        sessions: data.sessions
      }))
      .filter(data => data.sessions >= 2) // Minimum 2 sessions for reliability
      .sort((a, b) => b.averageScore - a.averageScore)
      .slice(0, 3)
      .map(data => data.hour);

    res.status(200).json({
      status: 'success',
      data: {
        totalSessions,
        completedSessions,
        completionRate: Math.round(completionRate * 100) / 100,
        totalPlannedTime,
        totalActualTime,
        averageFocusScore: Math.round(averageFocusScore * 100) / 100,
        sessionsByType,
        bestHours,
        analysisPeriod: `${days} days`
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get personalized focus recommendations
 */
const getFocusRecommendations = async (req, res, next) => {
  try {
    const thirtyDaysAgo = moment().subtract(30, 'days').toDate();

    const sessions = await FocusSession.findAll({
      where: {
        userId: req.user.id,
        startTime: { [Op.gte]: thirtyDaysAgo }
      }
    });

    const recommendations = [];

    // Analyze completion patterns
    const completedSessions = sessions.filter(s => s.completionStatus === 'completed');
    const completionRate = sessions.length > 0 ? (completedSessions.length / sessions.length) * 100 : 0;

    if (completionRate < 60) {
      recommendations.push({
        type: 'duration',
        priority: 'high',
        title: 'Try Shorter Sessions',
        description: 'Your completion rate is low. Consider starting with shorter 15-20 minute sessions.',
        action: 'Reduce default session duration'
      });
    }

    // Analyze distraction patterns
    const totalDistractions = sessions.reduce((sum, s) => sum + (s.distractionCount || 0), 0);
    const avgDistractions = sessions.length > 0 ? totalDistractions / sessions.length : 0;

    if (avgDistractions > 3) {
      recommendations.push({
        type: 'blocking',
        priority: 'high',
        title: 'Strengthen Your Blocking Rules',
        description: 'You have frequent distractions. Consider adding more sites to your blocklist.',
        action: 'Review and update blocking rules'
      });
    }

    // Analyze best performing times
    const hourlyPerformance = Array.from({ length: 24 }, () => ({ sessions: 0, totalScore: 0 }));
    sessions.forEach(session => {
      const hour = moment(session.startTime).hour();
      hourlyPerformance[hour].sessions++;
      hourlyPerformance[hour].totalScore += session.focusScore || 0;
    });

    const bestHours = hourlyPerformance
      .map((data, hour) => ({
        hour,
        averageScore: data.sessions > 0 ? data.totalScore / data.sessions : 0,
        sessions: data.sessions
      }))
      .filter(data => data.sessions >= 2)
      .sort((a, b) => b.averageScore - a.averageScore)
      .slice(0, 2);

    if (bestHours.length > 0) {
      const bestHour = bestHours[0].hour;
      const timeString = moment().hour(bestHour).format('h:mm A');

      recommendations.push({
        type: 'timing',
        priority: 'medium',
        title: 'Optimize Your Schedule',
        description: `Your best focus time is around ${timeString}. Try scheduling important work then.`,
        action: `Schedule focus sessions at ${timeString}`
      });
    }

    // Session type recommendations
    const pomodoroSessions = sessions.filter(s => s.type === 'pomodoro');
    const deepWorkSessions = sessions.filter(s => s.type === 'deep_work');

    if (pomodoroSessions.length > 0 && deepWorkSessions.length > 0) {
      const pomodoroAvgScore = pomodoroSessions.reduce((sum, s) => sum + (s.focusScore || 0), 0) / pomodoroSessions.length;
      const deepWorkAvgScore = deepWorkSessions.reduce((sum, s) => sum + (s.focusScore || 0), 0) / deepWorkSessions.length;

      if (pomodoroAvgScore > deepWorkAvgScore + 10) {
        recommendations.push({
          type: 'technique',
          priority: 'medium',
          title: 'Pomodoro Works Best for You',
          description: 'Your Pomodoro sessions have higher focus scores. Consider using this technique more often.',
          action: 'Use Pomodoro technique for important tasks'
        });
      } else if (deepWorkAvgScore > pomodoroAvgScore + 10) {
        recommendations.push({
          type: 'technique',
          priority: 'medium',
          title: 'Deep Work Sessions Are Your Strength',
          description: 'You perform better in longer, uninterrupted sessions.',
          action: 'Schedule longer deep work blocks'
        });
      }
    }

    // General recommendations based on data
    if (sessions.length < 5) {
      recommendations.push({
        type: 'habit',
        priority: 'low',
        title: 'Build a Consistent Focus Habit',
        description: 'Try to use focus mode daily to build better concentration habits.',
        action: 'Set a daily focus session goal'
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        recommendations,
        analysisData: {
          totalSessions: sessions.length,
          completionRate: Math.round(completionRate * 100) / 100,
          averageDistractions: Math.round(avgDistractions * 100) / 100,
          bestFocusHours: bestHours.map(h => h.hour)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getFocusSessions,
  createFocusSession,
  getFocusSession,
  updateFocusSession,
  deleteFocusSession,
  getActiveFocusSession,
  startFocusSession,
  stopFocusSession,
  pauseFocusSession,
  resumeFocusSession,
  getBlocklists,
  createBlocklist,
  updateBlocklist,
  deleteBlocklist,
  getPomodoroSettings,
  updatePomodoroSettings,
  startPomodoro,
  startPomodoroBreak,
  getDistractions,
  recordDistraction,
  getDistractionAnalysis,
  getFocusInsights,
  getFocusRecommendations
};
