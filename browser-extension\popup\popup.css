/* TimeWarp Browser Extension - Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 380px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  overflow-x: hidden;
}

.popup-container {
  background: white;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  animation: pulse 2s infinite;
}

.status-dot.active {
  background: #22c55e;
}

.status-dot.focus {
  background: #f59e0b;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Current Session */
.current-session {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.current-session h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.session-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-site {
  display: flex;
  align-items: center;
  gap: 8px;
}

.site-favicon {
  font-size: 16px;
}

.site-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.session-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.time-label {
  font-size: 11px;
  color: #6b7280;
}

.time-value {
  font-size: 16px;
  font-weight: 600;
  color: #059669;
  font-family: 'Courier New', monospace;
}

/* Focus Mode */
.focus-mode {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.focus-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.focus-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.focus-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 6px;
}

.setting-group select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.setting-group input[type="checkbox"] {
  margin: 0;
}

/* Focus Active */
.focus-active {
  text-align: center;
}

.focus-timer {
  margin: 20px 0;
}

.timer-circle {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
}

.timer-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.timer-bg {
  fill: none;
  stroke: #e5e7eb;
  stroke-width: 4;
}

.timer-progress {
  fill: none;
  stroke: #3b82f6;
  stroke-width: 4;
  stroke-linecap: round;
  stroke-dasharray: 283;
  stroke-dashoffset: 0;
  transition: stroke-dashoffset 1s ease;
}

.timer-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.timer-time {
  display: block;
  font-size: 20px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  color: #1f2937;
}

.timer-label {
  font-size: 11px;
  color: #6b7280;
}

.focus-controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin: 20px 0;
}

.focus-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
}

/* Quick Actions */
.quick-actions {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.quick-actions h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Daily Stats */
.daily-stats {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.daily-stats h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stat-icon {
  font-size: 20px;
  width: 32px;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-card .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.stat-card .stat-label {
  font-size: 12px;
  color: #6b7280;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-outline {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  flex: 1;
  text-align: left;
}

/* Footer */
.popup-footer {
  padding: 16px 20px;
  background: #f8fafc;
  margin-top: auto;
}

.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.link-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: color 0.2s;
}

.link-btn:hover {
  color: #374151;
  background: #e5e7eb;
}

.link-btn.premium {
  color: #f59e0b;
  font-weight: 600;
}

.link-btn.premium:hover {
  color: #d97706;
  background: #fef3c7;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #6b7280;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  body {
    width: 320px;
  }
  
  .popup-container {
    min-height: 480px;
  }
}
