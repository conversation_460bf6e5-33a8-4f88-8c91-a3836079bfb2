# TimeWarp Frontend Dockerfile
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY frontend/ .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy custom nginx config
COPY docker/nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S nginx && \
    adduser -S timewarp -u 1001 -G nginx

# Change ownership
RUN chown -R timewarp:nginx /usr/share/nginx/html && \
    chown -R timewarp:nginx /var/cache/nginx && \
    chown -R timewarp:nginx /var/log/nginx && \
    chown -R timewarp:nginx /etc/nginx/conf.d

# Switch to non-root user
USER timewarp

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
