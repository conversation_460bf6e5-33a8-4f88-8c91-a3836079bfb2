const fs = require('fs');
const path = require('path');

const controllers = [
  'taskController',
  'calendarController', 
  'analyticsController',
  'teamController',
  'focusController',
  'aiController'
];

const template = (name, title) => `/**
 * ${title}
 * Placeholder controller - to be implemented
 */

const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Placeholder implementations - to be completed when models are created

const placeholder = async (req, res, next) => {
  try {
    res.status(501).json({
      status: 'error',
      message: '${title} endpoints not implemented yet'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Export placeholder for all methods
  placeholder
};
`;

controllers.forEach(controller => {
  const title = controller.replace('Controller', '').replace(/([A-Z])/g, ' $1').trim();
  const content = template(controller, title.charAt(0).toUpperCase() + title.slice(1) + ' Controller');
  fs.writeFileSync(path.join('backend/src/controllers', `${controller}.js`), content);
  console.log(`Created ${controller}.js`);
});

console.log('All controllers created!');
