const { featureGateService, FeatureRestrictedError } = require('../services/featureGateService')

// Middleware to check if user has access to a specific feature
const requireFeature = (feature) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        })
      }

      const access = await featureGateService.checkFeatureAccess(userId, feature)
      
      // For boolean features
      if (typeof access === 'boolean') {
        if (!access) {
          const upgradePrompt = await featureGateService.shouldShowUpgradePrompt(userId, feature)
          return res.status(403).json({
            success: false,
            message: `Feature '${feature}' requires a premium subscription`,
            feature,
            upgradePrompt: upgradePrompt.show ? upgradePrompt : null,
            code: 'FEATURE_RESTRICTED'
          })
        }
      }
      
      // For limit-based features
      if (typeof access === 'object' && !access.hasAccess) {
        const upgradePrompt = await featureGateService.shouldShowUpgradePrompt(userId, feature)
        return res.status(403).json({
          success: false,
          message: `You've reached your ${feature} limit`,
          feature,
          usage: access,
          upgradePrompt: upgradePrompt.show ? upgradePrompt : null,
          code: 'USAGE_LIMIT_REACHED'
        })
      }

      // Add feature access info to request
      req.featureAccess = access
      next()
    } catch (error) {
      console.error('Feature gate error:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to check feature access'
      })
    }
  }
}

// Middleware to enforce usage limits when creating resources
const enforceUsageLimit = (feature, increment = 1) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        })
      }

      try {
        const result = await featureGateService.enforceFeatureLimit(userId, feature, increment)
        req.usageUpdate = result
        next()
      } catch (error) {
        if (error instanceof FeatureRestrictedError) {
          const upgradePrompt = await featureGateService.shouldShowUpgradePrompt(userId, feature)
          return res.status(403).json({
            success: false,
            message: error.message,
            feature: error.feature,
            usage: error.usage,
            upgradePrompt: upgradePrompt.show ? upgradePrompt : null,
            code: 'FEATURE_RESTRICTED'
          })
        }
        throw error
      }
    } catch (error) {
      console.error('Usage limit enforcement error:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to enforce usage limit'
      })
    }
  }
}

// Middleware to add usage summary to response
const addUsageSummary = async (req, res, next) => {
  try {
    const userId = req.user?.id
    if (userId) {
      const usageSummary = await featureGateService.getUsageSummary(userId)
      req.usageSummary = usageSummary
    }
    next()
  } catch (error) {
    console.error('Usage summary error:', error)
    // Don't fail the request, just continue without usage summary
    next()
  }
}

// Middleware to check if upgrade prompt should be shown
const checkUpgradePrompts = (features = []) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id
      if (!userId) {
        return next()
      }

      const prompts = []
      for (const feature of features) {
        const prompt = await featureGateService.shouldShowUpgradePrompt(userId, feature)
        if (prompt.show) {
          prompts.push({
            feature,
            ...prompt
          })
        }
      }

      req.upgradePrompts = prompts
      next()
    } catch (error) {
      console.error('Upgrade prompt check error:', error)
      // Don't fail the request
      next()
    }
  }
}

// Middleware to validate team member limits
const validateTeamMemberLimit = async (req, res, next) => {
  try {
    const userId = req.user?.id
    const teamId = req.params.teamId || req.body.teamId

    if (!userId || !teamId) {
      return next()
    }

    // Get current team member count
    const { TeamMember } = require('../models')
    const currentCount = await TeamMember.count({
      where: { teamId, isActive: true }
    })

    // Check if adding one more member would exceed limit
    const access = await featureGateService.checkFeatureAccess(userId, 'teamMembers')
    
    if (typeof access === 'object') {
      if (access.limit !== -1 && currentCount >= access.limit) {
        const upgradePrompt = await featureGateService.shouldShowUpgradePrompt(userId, 'teamMembers')
        return res.status(403).json({
          success: false,
          message: `Team member limit reached (${access.limit} members)`,
          feature: 'teamMembers',
          usage: {
            current: currentCount,
            limit: access.limit,
            remaining: 0
          },
          upgradePrompt: upgradePrompt.show ? upgradePrompt : null,
          code: 'TEAM_MEMBER_LIMIT_REACHED'
        })
      }
    }

    next()
  } catch (error) {
    console.error('Team member limit validation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to validate team member limit'
    })
  }
}

// Middleware to add feature flags to response
const addFeatureFlags = async (req, res, next) => {
  try {
    const userId = req.user?.id
    if (!userId) {
      return next()
    }

    const subscription = await featureGateService.getUserSubscription(userId)
    const featureFlags = {
      plan: subscription.plan,
      features: subscription.features,
      canUpgrade: subscription.canUpgrade(),
      isTrialing: subscription.isTrialing(),
      daysUntilExpiry: subscription.daysUntilExpiry()
    }

    req.featureFlags = featureFlags
    next()
  } catch (error) {
    console.error('Feature flags error:', error)
    // Don't fail the request
    next()
  }
}

// Helper function to create feature-specific middleware
const createFeatureMiddleware = (feature) => ({
  require: requireFeature(feature),
  enforce: (increment = 1) => enforceUsageLimit(feature, increment),
  check: checkUpgradePrompts([feature])
})

// Export commonly used feature middleware
const features = {
  projects: createFeatureMiddleware('projects'),
  teamMembers: createFeatureMiddleware('teamMembers'),
  workspaces: createFeatureMiddleware('workspaces'),
  advancedAnalytics: createFeatureMiddleware('advancedAnalytics'),
  teamCollaboration: createFeatureMiddleware('teamCollaboration'),
  exportData: createFeatureMiddleware('exportData'),
  apiAccess: createFeatureMiddleware('apiAccess'),
  customIntegrations: createFeatureMiddleware('customIntegrations')
}

module.exports = {
  requireFeature,
  enforceUsageLimit,
  addUsageSummary,
  checkUpgradePrompts,
  validateTeamMemberLimit,
  addFeatureFlags,
  createFeatureMiddleware,
  features
}
