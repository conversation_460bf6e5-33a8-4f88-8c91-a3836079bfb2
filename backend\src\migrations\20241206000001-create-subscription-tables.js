'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Subscriptions table
    await queryInterface.createTable('Subscriptions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      teamId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'Teams',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      plan: {
        type: Sequelize.ENUM('free', 'team', 'business', 'enterprise'),
        allowNull: false,
        defaultValue: 'free'
      },
      status: {
        type: Sequelize.ENUM('active', 'canceled', 'past_due', 'trialing', 'incomplete'),
        allowNull: false,
        defaultValue: 'active'
      },
      billingCycle: {
        type: Sequelize.ENUM('monthly', 'yearly'),
        allowNull: true
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      currency: {
        type: Sequelize.STRING(3),
        allowNull: true,
        defaultValue: 'USD'
      },
      features: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {}
      },
      usage: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {}
      },
      paymentProviderId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      paymentProvider: {
        type: Sequelize.ENUM('stripe', 'payoneer', 'paypal'),
        allowNull: true
      },
      customerId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      currentPeriodStart: {
        type: Sequelize.DATE,
        allowNull: true
      },
      currentPeriodEnd: {
        type: Sequelize.DATE,
        allowNull: true
      },
      trialStart: {
        type: Sequelize.DATE,
        allowNull: true
      },
      trialEnd: {
        type: Sequelize.DATE,
        allowNull: true
      },
      cancelAtPeriodEnd: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      canceledAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      nextBillingDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {}
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create Payments table
    await queryInterface.createTable('Payments', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      subscriptionId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'Subscriptions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      currency: {
        type: Sequelize.STRING(3),
        allowNull: false,
        defaultValue: 'USD'
      },
      status: {
        type: Sequelize.ENUM('pending', 'succeeded', 'failed', 'canceled', 'refunded'),
        allowNull: false,
        defaultValue: 'pending'
      },
      type: {
        type: Sequelize.ENUM('subscription', 'one_time', 'refund'),
        allowNull: false,
        defaultValue: 'subscription'
      },
      paymentProvider: {
        type: Sequelize.ENUM('stripe', 'payoneer', 'paypal'),
        allowNull: false
      },
      paymentProviderId: {
        type: Sequelize.STRING,
        allowNull: false
      },
      paymentMethodId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      invoiceId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      receiptUrl: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      failureReason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      refundAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      refundReason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      paidAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      refundedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {}
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create SubscriptionHistory table
    await queryInterface.createTable('SubscriptionHistories', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      subscriptionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Subscriptions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      action: {
        type: Sequelize.ENUM(
          'created', 'plan_changed', 'payment_succeeded', 'payment_failed',
          'canceled', 'upgraded', 'downgraded', 'refunded', 'reactivated'
        ),
        allowNull: false
      },
      oldValue: {
        type: Sequelize.STRING,
        allowNull: true
      },
      newValue: {
        type: Sequelize.STRING,
        allowNull: true
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      currency: {
        type: Sequelize.STRING(3),
        allowNull: true
      },
      paymentProviderId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      source: {
        type: Sequelize.ENUM('user', 'admin', 'system', 'webhook'),
        allowNull: false,
        defaultValue: 'user'
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {}
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('Subscriptions', ['userId']);
    await queryInterface.addIndex('Subscriptions', ['teamId']);
    await queryInterface.addIndex('Subscriptions', ['plan']);
    await queryInterface.addIndex('Subscriptions', ['status']);
    await queryInterface.addIndex('Subscriptions', ['paymentProviderId']);
    await queryInterface.addIndex('Subscriptions', ['nextBillingDate']);

    await queryInterface.addIndex('Payments', ['userId']);
    await queryInterface.addIndex('Payments', ['subscriptionId']);
    await queryInterface.addIndex('Payments', ['status']);
    await queryInterface.addIndex('Payments', ['paymentProviderId']);
    await queryInterface.addIndex('Payments', ['paidAt']);

    await queryInterface.addIndex('SubscriptionHistories', ['subscriptionId']);
    await queryInterface.addIndex('SubscriptionHistories', ['userId']);
    await queryInterface.addIndex('SubscriptionHistories', ['action']);
    await queryInterface.addIndex('SubscriptionHistories', ['createdAt']);
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order due to foreign key constraints
    await queryInterface.dropTable('SubscriptionHistories');
    await queryInterface.dropTable('Payments');
    await queryInterface.dropTable('Subscriptions');
  }
};
