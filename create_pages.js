const fs = require('fs');
const path = require('path');

const pages = [
  'Landing',
  'TimeTracking', 
  'Tasks',
  'Calendar',
  'Analytics',
  'Focus',
  'Teams',
  'Settings',
  'Profile'
];

const authPages = [
  'Register',
  'ForgotPassword',
  'ResetPassword'
];

const pageTemplate = (name) => `import React from 'react'
import { motion } from 'framer-motion'

const ${name} = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          ${name}
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          ${name} page content will be implemented here.
        </p>
      </div>

      <div className="card p-8 text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Coming Soon
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          This feature is currently under development and will be available soon.
        </p>
      </div>
    </motion.div>
  )
}

export default ${name}
`;

// Create main pages
pages.forEach(page => {
  const content = pageTemplate(page);
  fs.writeFileSync(path.join('frontend/src/pages', `${page}.jsx`), content);
  console.log(`Created ${page}.jsx`);
});

// Create auth pages
authPages.forEach(page => {
  const content = pageTemplate(page);
  fs.writeFileSync(path.join('frontend/src/pages/Auth', `${page}.jsx`), content);
  console.log(`Created Auth/${page}.jsx`);
});

console.log('All pages created!');
