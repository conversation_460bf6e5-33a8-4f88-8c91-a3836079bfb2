/**
 * Token Service
 * Handles JWT token storage and management
 */

const TOKEN_KEY = 'timewarp_token'
const REFRESH_TOKEN_KEY = 'timewarp_refresh_token'

export const tokenService = {
  /**
   * Get the current access token
   */
  getToken() {
    try {
      return localStorage.getItem(TOKEN_KEY)
    } catch (error) {
      console.error('Error getting token:', error)
      return null
    }
  },

  /**
   * Set the access token
   */
  setToken(token) {
    try {
      if (token) {
        localStorage.setItem(TOKEN_KEY, token)
      } else {
        localStorage.removeItem(TOKEN_KEY)
      }
    } catch (error) {
      console.error('Error setting token:', error)
    }
  },

  /**
   * Get the refresh token
   */
  getRefreshToken() {
    try {
      return localStorage.getItem(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.error('Error getting refresh token:', error)
      return null
    }
  },

  /**
   * Set the refresh token
   */
  setRefreshToken(token) {
    try {
      if (token) {
        localStorage.setItem(REFRESH_TOKEN_KEY, token)
      } else {
        localStorage.removeItem(REFRESH_TOKEN_KEY)
      }
    } catch (error) {
      console.error('Error setting refresh token:', error)
    }
  },

  /**
   * Remove all tokens
   */
  removeToken() {
    try {
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.error('Error removing tokens:', error)
    }
  },

  /**
   * Check if token exists
   */
  hasToken() {
    return !!this.getToken()
  },

  /**
   * Decode JWT token payload
   */
  decodeToken(token = null) {
    try {
      const tokenToUse = token || this.getToken()
      if (!tokenToUse) return null

      const base64Url = tokenToUse.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )

      return JSON.parse(jsonPayload)
    } catch (error) {
      console.error('Error decoding token:', error)
      return null
    }
  },

  /**
   * Check if token is expired
   */
  isTokenExpired(token = null) {
    try {
      const decoded = this.decodeToken(token)
      if (!decoded || !decoded.exp) return true

      const currentTime = Date.now() / 1000
      return decoded.exp < currentTime
    } catch (error) {
      console.error('Error checking token expiration:', error)
      return true
    }
  },

  /**
   * Get token expiration time
   */
  getTokenExpiration(token = null) {
    try {
      const decoded = this.decodeToken(token)
      if (!decoded || !decoded.exp) return null

      return new Date(decoded.exp * 1000)
    } catch (error) {
      console.error('Error getting token expiration:', error)
      return null
    }
  },

  /**
   * Get user info from token
   */
  getUserFromToken(token = null) {
    try {
      const decoded = this.decodeToken(token)
      if (!decoded) return null

      return {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role
      }
    } catch (error) {
      console.error('Error getting user from token:', error)
      return null
    }
  },

  /**
   * Check if token will expire soon (within 5 minutes)
   */
  willTokenExpireSoon(token = null) {
    try {
      const decoded = this.decodeToken(token)
      if (!decoded || !decoded.exp) return true

      const currentTime = Date.now() / 1000
      const fiveMinutesFromNow = currentTime + (5 * 60) // 5 minutes in seconds

      return decoded.exp < fiveMinutesFromNow
    } catch (error) {
      console.error('Error checking if token will expire soon:', error)
      return true
    }
  },

  /**
   * Clear all authentication data
   */
  clearAuth() {
    this.removeToken()
    // Clear any other auth-related data from localStorage
    try {
      const keysToRemove = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('timewarp_')) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.error('Error clearing auth data:', error)
    }
  }
}
