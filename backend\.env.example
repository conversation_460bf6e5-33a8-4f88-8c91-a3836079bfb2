# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/timewarp
DB_HOST=localhost
DB_PORT=5432
DB_NAME=timewarp
DB_USER=username
DB_PASSWORD=password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Server Configuration
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# Stripe Configuration (Primary Payment Provider)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs (Create these in your Stripe Dashboard)
# Team Plan
STRIPE_TEAM_MONTHLY_PRICE_ID=price_team_monthly_id
STRIPE_TEAM_YEARLY_PRICE_ID=price_team_yearly_id

# Business Plan
STRIPE_BUSINESS_MONTHLY_PRICE_ID=price_business_monthly_id
STRIPE_BUSINESS_YEARLY_PRICE_ID=price_business_yearly_id

# Enterprise Plan
STRIPE_ENTERPRISE_MONTHLY_PRICE_ID=price_enterprise_monthly_id
STRIPE_ENTERPRISE_YEARLY_PRICE_ID=price_enterprise_yearly_id

# Email Configuration (for notifications and receipts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=TimeWarp

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Feature Flags
ENABLE_TEAM_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_INTEGRATIONS=true
ENABLE_API_ACCESS=true

# External API Keys (for integrations)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret

# Monitoring and Logging
LOG_LEVEL=info
SENTRY_DSN=your-sentry-dsn-here

# Development/Testing
SEED_DATABASE=false
ENABLE_DEBUG_ROUTES=false

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=timewarp-backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
