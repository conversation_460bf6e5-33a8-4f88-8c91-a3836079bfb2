version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: timewarp-postgres
    environment:
      POSTGRES_DB: timewarp
      POSTGRES_USER: timewarp
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-timewarp123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - timewarp-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U timewarp -d timewarp"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: timewarp-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - timewarp-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: timewarp-backend
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://timewarp:${POSTGRES_PASSWORD:-timewarp123}@postgres:5432/timewarp
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRES_IN: 7d
      FRONTEND_URL: http://localhost:3000
      BACKEND_URL: http://localhost:3001
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - timewarp-network
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Web App
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    container_name: timewarp-frontend
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - timewarp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: timewarp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx-prod.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - timewarp-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  timewarp-network:
    driver: bridge
