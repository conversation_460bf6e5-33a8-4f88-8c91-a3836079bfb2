import React, { useState, useEffect } from 'react'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { CheckIcon, XMarkIcon, StarIcon } from '@heroicons/react/24/solid'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'

const PricingPlans = ({ targetFeature = null, onPlanSelect = null }) => {
  const {
    plans,
    subscription,
    loading,
    loadPlans,
    createSubscription,
    updateSubscription
  } = useSubscription()
  
  const [billingCycle, setBillingCycle] = useState('monthly')
  const [selectedPlan, setSelectedPlan] = useState(null)
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    loadPlans(targetFeature)
  }, [targetFeature])

  const handlePlanSelect = async (planKey) => {
    if (processing) return
    
    setSelectedPlan(planKey)
    setProcessing(true)

    try {
      if (onPlanSelect) {
        onPlanSelect(planKey, billingCycle)
        return
      }

      // Handle subscription creation/update
      if (!subscription || subscription.plan === 'free') {
        // Create new subscription
        const result = await createSubscription(planKey, billingCycle)
        if (result) {
          toast.success('Subscription created successfully!')
        }
      } else {
        // Update existing subscription
        const success = await updateSubscription(planKey, billingCycle)
        if (success) {
          toast.success('Subscription updated successfully!')
        }
      }
    } catch (error) {
      toast.error('Failed to process subscription')
    } finally {
      setProcessing(false)
      setSelectedPlan(null)
    }
  }

  const formatFeatureValue = (value, feature) => {
    if (value === true) return 'Included'
    if (value === false) return 'Not included'
    if (value === -1) return 'Unlimited'
    if (typeof value === 'number') {
      if (feature.includes('GB')) return `${value} GB`
      if (feature.includes('Hours')) return value === -1 ? 'Unlimited' : `${value} hours`
      return value.toLocaleString()
    }
    return value
  }

  const getButtonText = (planKey) => {
    if (processing && selectedPlan === planKey) return 'Processing...'
    if (subscription?.plan === planKey) return 'Current Plan'
    if (subscription?.plan === 'free') return 'Start Free Trial'
    return 'Upgrade'
  }

  const getButtonStyle = (planKey) => {
    const isCurrentPlan = subscription?.plan === planKey
    const isProcessing = processing && selectedPlan === planKey
    
    if (isCurrentPlan) {
      return 'bg-gray-100 text-gray-500 cursor-not-allowed'
    }
    
    if (planKey === 'team') {
      return `bg-blue-600 hover:bg-blue-700 text-white ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`
    }
    
    return `bg-gray-900 hover:bg-gray-800 text-white ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`
  }

  if (loading || !plans) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const planOrder = ['free', 'team', 'business', 'enterprise']
  const orderedPlans = planOrder.map(key => ({ key, ...plans.plans[key] }))

  return (
    <div className="py-12">
      {/* Billing Toggle */}
      <div className="flex justify-center mb-8">
        <div className="bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setBillingCycle('monthly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'monthly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-900'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setBillingCycle('yearly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'yearly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-900'
            }`}
          >
            Yearly
            <span className="ml-1 text-xs text-green-600 font-semibold">Save 17%</span>
          </button>
        </div>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto px-4">
        {orderedPlans.map((plan, index) => (
          <motion.div
            key={plan.key}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`relative bg-white rounded-2xl shadow-lg border-2 transition-all duration-200 hover:shadow-xl ${
              plan.popular ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20' : 'border-gray-200'
            } ${subscription?.plan === plan.key ? 'ring-2 ring-green-500 ring-opacity-30' : ''}`}
          >
            {/* Popular Badge */}
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center">
                  <StarIcon className="w-3 h-3 mr-1" />
                  Most Popular
                </div>
              </div>
            )}

            {/* Current Plan Badge */}
            {subscription?.plan === plan.key && (
              <div className="absolute -top-3 right-4">
                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                  Current Plan
                </div>
              </div>
            )}

            <div className="p-6">
              {/* Plan Header */}
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900">
                    ${plan.price[billingCycle]}
                  </span>
                  <span className="text-gray-500 ml-1">
                    /{billingCycle === 'yearly' ? 'year' : 'month'}
                  </span>
                </div>
                {billingCycle === 'yearly' && plan.savings && (
                  <div className="text-sm text-green-600 font-medium">
                    Save {plan.savings}% annually
                  </div>
                )}
              </div>

              {/* Features List */}
              <div className="space-y-3 mb-6">
                {Object.entries(plan.features).map(([feature, value]) => {
                  const isIncluded = value === true || value === -1 || (typeof value === 'number' && value > 0)
                  
                  return (
                    <div key={feature} className="flex items-start">
                      {isIncluded ? (
                        <CheckIcon className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      ) : (
                        <XMarkIcon className="w-5 h-5 text-gray-300 mr-3 mt-0.5 flex-shrink-0" />
                      )}
                      <div className="text-sm">
                        <div className={`font-medium ${isIncluded ? 'text-gray-900' : 'text-gray-400'}`}>
                          {feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </div>
                        <div className={`text-xs ${isIncluded ? 'text-gray-600' : 'text-gray-400'}`}>
                          {formatFeatureValue(value, feature)}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* CTA Button */}
              <button
                onClick={() => handlePlanSelect(plan.key)}
                disabled={subscription?.plan === plan.key || (processing && selectedPlan === plan.key)}
                className={`w-full py-3 px-4 rounded-lg font-semibold text-sm transition-colors ${getButtonStyle(plan.key)}`}
              >
                {getButtonText(plan.key)}
              </button>

              {/* Free Trial Notice */}
              {plan.key !== 'free' && (!subscription || subscription.plan === 'free') && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  14-day free trial included
                </p>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Feature Comparison Note */}
      {targetFeature && (
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Upgrade to unlock <span className="font-semibold">{targetFeature}</span> and boost your productivity
          </p>
        </div>
      )}

      {/* Money Back Guarantee */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500">
          30-day money-back guarantee • Cancel anytime • No setup fees
        </p>
      </div>
    </div>
  )
}

export default PricingPlans
