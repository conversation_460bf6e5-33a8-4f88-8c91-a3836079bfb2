import React, { useState } from 'react'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { 
  ChartBarIcon, 
  UsersIcon, 
  FolderIcon, 
  CubeIcon,
  ArrowUpIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { motion } from 'framer-motion'
import UpgradePrompt from './UpgradePrompt'

const UsageDashboard = () => {
  const {
    subscription,
    usage,
    loading,
    getUsagePercentage,
    isAtLimit,
    shouldShowUpgradePrompt,
    hasFeature
  } = useSubscription()

  const [showUpgradePrompt, setShowUpgradePrompt] = useState(null)

  if (loading || !subscription || !usage) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    )
  }

  const getFeatureIcon = (feature) => {
    const icons = {
      projects: FolderIcon,
      teamMembers: UsersIcon,
      workspaces: CubeIcon,
      storage: ChartBarIcon
    }
    return icons[feature] || ChartBarIcon
  }

  const getFeatureDisplayName = (feature) => {
    const names = {
      projects: 'Projects',
      teamMembers: 'Team Members',
      workspaces: 'Workspaces',
      storage: 'Storage',
      advancedAnalytics: 'Advanced Analytics',
      exportData: 'Data Export',
      apiAccess: 'API Access'
    }
    return names[feature] || feature
  }

  const formatUsageValue = (value, feature) => {
    if (feature === 'storage') {
      return `${(value / 1024 / 1024 / 1024).toFixed(1)} GB`
    }
    return value.toLocaleString()
  }

  const formatLimitValue = (limit, feature) => {
    if (limit === -1) return 'Unlimited'
    if (feature === 'storage') {
      return `${limit} GB`
    }
    return limit.toLocaleString()
  }

  const getUsageColor = (percentage) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const getProgressColor = (percentage) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-blue-500'
  }

  const limitBasedFeatures = Object.entries(usage.features).filter(
    ([_, data]) => data.type === 'limit'
  )

  const booleanFeatures = Object.entries(usage.features).filter(
    ([_, data]) => data.type === 'boolean'
  )

  return (
    <div className="space-y-6">
      {/* Plan Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 capitalize">
              {subscription.plan} Plan
            </h2>
            <p className="text-sm text-gray-600">
              {subscription.status === 'trialing' ? (
                <>
                  Trial ends in {subscription.daysUntilExpiry} days
                </>
              ) : (
                <>
                  Next billing: {new Date(subscription.nextBillingDate).toLocaleDateString()}
                </>
              )}
            </p>
          </div>
          {subscription.plan !== 'enterprise' && (
            <button
              onClick={() => setShowUpgradePrompt('general')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <ArrowUpIcon className="w-4 h-4" />
              <span>Upgrade</span>
            </button>
          )}
        </div>

        {subscription.status === 'trialing' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-blue-900">
                  Free trial active
                </p>
                <p className="text-xs text-blue-700">
                  Your trial ends in {subscription.daysUntilExpiry} days. 
                  Upgrade now to continue using premium features.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Usage Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {limitBasedFeatures.map(([feature, data]) => {
          const percentage = getUsagePercentage(feature)
          const Icon = getFeatureIcon(feature)
          const atLimit = isAtLimit(feature)
          const shouldPrompt = shouldShowUpgradePrompt(feature)

          return (
            <motion.div
              key={feature}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`bg-white rounded-lg shadow-sm border-2 p-6 transition-all duration-200 hover:shadow-md ${
                atLimit ? 'border-red-200 bg-red-50' : 'border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${atLimit ? 'bg-red-100' : 'bg-blue-100'}`}>
                    <Icon className={`w-5 h-5 ${atLimit ? 'text-red-600' : 'text-blue-600'}`} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {getFeatureDisplayName(feature)}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {formatUsageValue(data.usage, feature)} of {formatLimitValue(data.limit, feature)}
                    </p>
                  </div>
                </div>
                {atLimit && (
                  <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
                )}
              </div>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Usage</span>
                  <span className={getUsageColor(percentage)}>
                    {Math.round(percentage)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(percentage)}`}
                    style={{ width: `${Math.min(100, percentage)}%` }}
                  ></div>
                </div>
              </div>

              {/* Action Button */}
              {(atLimit || shouldPrompt) && (
                <button
                  onClick={() => setShowUpgradePrompt(feature)}
                  className={`w-full py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                    atLimit
                      ? 'bg-red-600 text-white hover:bg-red-700'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {atLimit ? 'Upgrade to Continue' : 'Upgrade for More'}
                </button>
              )}
            </motion.div>
          )
        })}
      </div>

      {/* Feature Access */}
      {booleanFeatures.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Feature Access</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {booleanFeatures.map(([feature, data]) => {
              const hasAccess = hasFeature(feature)
              const Icon = getFeatureIcon(feature)

              return (
                <div
                  key={feature}
                  className={`flex items-center justify-between p-4 rounded-lg border-2 ${
                    hasAccess ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${hasAccess ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Icon className={`w-4 h-4 ${hasAccess ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <span className={`font-medium ${hasAccess ? 'text-green-900' : 'text-gray-500'}`}>
                      {getFeatureDisplayName(feature)}
                    </span>
                  </div>
                  {hasAccess ? (
                    <span className="text-xs font-semibold text-green-600 bg-green-100 px-2 py-1 rounded-full">
                      Included
                    </span>
                  ) : (
                    <button
                      onClick={() => setShowUpgradePrompt(feature)}
                      className="text-xs font-semibold text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      Upgrade
                    </button>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Upgrade Prompt Modal */}
      {showUpgradePrompt && (
        <UpgradePrompt
          feature={showUpgradePrompt}
          onClose={() => setShowUpgradePrompt(null)}
        />
      )}
    </div>
  )
}

export default UsageDashboard
