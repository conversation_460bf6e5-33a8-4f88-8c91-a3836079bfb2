import React from 'react'
import { motion } from 'framer-motion'
import {
  ClockIcon,
  CheckCircleIcon,
  ChartBarIcon,
  FireIcon,
  CalendarIcon,
  UsersIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '@context/AuthContext'

const Dashboard = () => {
  const { user } = useAuth()

  // Mock data for demonstration
  const stats = [
    {
      name: 'Time Tracked Today',
      value: '6h 32m',
      change: '+12%',
      changeType: 'increase',
      icon: ClockIcon,
      color: 'text-blue-600'
    },
    {
      name: 'Tasks Completed',
      value: '12',
      change: '+3',
      changeType: 'increase',
      icon: CheckCircleIcon,
      color: 'text-green-600'
    },
    {
      name: 'Productivity Score',
      value: '87%',
      change: '+5%',
      changeType: 'increase',
      icon: ChartBarIcon,
      color: 'text-purple-600'
    },
    {
      name: 'Focus Sessions',
      value: '4',
      change: '+1',
      changeType: 'increase',
      icon: FireIcon,
      color: 'text-orange-600'
    }
  ]

  const recentTasks = [
    { id: 1, title: 'Complete TimeWarp backend', status: 'in_progress', priority: 'high' },
    { id: 2, title: 'Review pull requests', status: 'completed', priority: 'medium' },
    { id: 3, title: 'Team standup meeting', status: 'completed', priority: 'low' },
    { id: 4, title: 'Update documentation', status: 'pending', priority: 'medium' }
  ]

  const upcomingEvents = [
    { id: 1, title: 'Team Meeting', time: '2:00 PM', type: 'meeting' },
    { id: 2, title: 'Code Review', time: '3:30 PM', type: 'review' },
    { id: 3, title: 'Project Demo', time: '4:00 PM', type: 'demo' }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Welcome back, {user?.firstName}! 👋
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Here's what's happening with your productivity today.
        </p>
      </motion.div>

      {/* Stats grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}
            className="card p-6"
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-lg bg-gray-100 dark:bg-gray-700`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.name}
                </p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                  <p className={`ml-2 text-sm font-medium ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent tasks */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="lg:col-span-2"
        >
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Recent Tasks
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                {recentTasks.map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        task.status === 'completed' ? 'bg-green-400' :
                        task.status === 'in_progress' ? 'bg-blue-400' : 'bg-gray-400'
                      }`} />
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {task.title}
                      </span>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      task.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {task.priority}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Upcoming events */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Upcoming Events
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-3">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {event.title}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {event.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Quick actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="card"
      >
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Quick Actions
          </h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="btn btn-primary">
              <ClockIcon className="h-5 w-5 mr-2" />
              Start Tracking
            </button>
            <button className="btn btn-secondary">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Add Task
            </button>
            <button className="btn btn-secondary">
              <FireIcon className="h-5 w-5 mr-2" />
              Focus Mode
            </button>
            <button className="btn btn-secondary">
              <ChartBarIcon className="h-5 w-5 mr-2" />
              View Analytics
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default Dashboard
