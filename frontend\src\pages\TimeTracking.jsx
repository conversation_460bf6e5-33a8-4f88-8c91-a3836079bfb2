import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  ClockIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CalendarIcon,
  TagIcon,
  FolderIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@services/api'
import { useSocket } from '@context/SocketContext'
import Loading, { CardSkeleton } from '@components/Loading'
import toast from 'react-hot-toast'

const TimeTracking = () => {
  const [isTracking, setIsTracking] = useState(false)
  const [currentSession, setCurrentSession] = useState(null)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [showManualEntry, setShowManualEntry] = useState(false)
  const [selectedProject, setSelectedProject] = useState('')
  const [description, setDescription] = useState('')
  const [filter, setFilter] = useState('all')
  const [dateRange, setDateRange] = useState('today')

  const queryClient = useQueryClient()
  const { socket, isConnected } = useSocket()

  // Fetch active session
  const { data: activeSession, isLoading: activeLoading } = useQuery(
    'activeSession',
    () => apiHelpers.get('/tracking/active'),
    {
      onSuccess: (response) => {
        const session = response.data.data.session
        if (session) {
          setCurrentSession(session)
          setIsTracking(true)
          setSelectedProject(session.projectId || '')
          setDescription(session.description || '')
        }
      }
    }
  )

  // Fetch recent sessions
  const { data: sessionsData, isLoading: sessionsLoading } = useQuery(
    ['sessions', filter, dateRange],
    () => {
      const params = new URLSearchParams()
      if (dateRange === 'today') {
        params.append('startDate', new Date().toISOString().split('T')[0])
      } else if (dateRange === 'week') {
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        params.append('startDate', weekAgo.toISOString().split('T')[0])
      }
      if (filter !== 'all') {
        params.append('projectId', filter)
      }
      return apiHelpers.get(`/tracking/sessions?${params.toString()}`)
    },
    {
      select: (response) => response.data.data.sessions
    }
  )

  // Fetch projects for dropdown
  const { data: projects } = useQuery(
    'projects',
    () => apiHelpers.get('/projects'),
    {
      select: (response) => response.data.data.projects
    }
  )

  // Start tracking mutation
  const startTrackingMutation = useMutation(
    (data) => apiHelpers.post('/tracking/start', data),
    {
      onSuccess: (response) => {
        const session = response.data.data.session
        setCurrentSession(session)
        setIsTracking(true)
        queryClient.invalidateQueries('activeSession')
        queryClient.invalidateQueries('sessions')
        toast.success('Time tracking started!')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to start tracking')
      }
    }
  )

  // Stop tracking mutation
  const stopTrackingMutation = useMutation(
    () => apiHelpers.post('/tracking/stop'),
    {
      onSuccess: () => {
        setCurrentSession(null)
        setIsTracking(false)
        setElapsedTime(0)
        setDescription('')
        queryClient.invalidateQueries('activeSession')
        queryClient.invalidateQueries('sessions')
        toast.success('Time tracking stopped!')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to stop tracking')
      }
    }
  )

  // Pause tracking mutation
  const pauseTrackingMutation = useMutation(
    () => apiHelpers.post('/tracking/pause'),
    {
      onSuccess: () => {
        setIsTracking(false)
        queryClient.invalidateQueries('activeSession')
        toast.success('Time tracking paused')
      }
    }
  )

  // Resume tracking mutation
  const resumeTrackingMutation = useMutation(
    () => apiHelpers.post('/tracking/resume'),
    {
      onSuccess: () => {
        setIsTracking(true)
        queryClient.invalidateQueries('activeSession')
        toast.success('Time tracking resumed')
      }
    }
  )

  // Manual entry mutation
  const manualEntryMutation = useMutation(
    (data) => apiHelpers.post('/tracking/sessions', data),
    {
      onSuccess: () => {
        setShowManualEntry(false)
        queryClient.invalidateQueries('sessions')
        toast.success('Manual entry added!')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to add manual entry')
      }
    }
  )

  // Delete session mutation
  const deleteSessionMutation = useMutation(
    (sessionId) => apiHelpers.delete(`/tracking/sessions/${sessionId}`),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('sessions')
        toast.success('Session deleted')
      }
    }
  )

  // Timer effect
  useEffect(() => {
    let interval = null
    if (isTracking && currentSession) {
      interval = setInterval(() => {
        const startTime = new Date(currentSession.startTime)
        const now = new Date()
        const elapsed = Math.floor((now - startTime) / 1000)
        setElapsedTime(elapsed)
      }, 1000)
    } else {
      setElapsedTime(0)
    }
    return () => clearInterval(interval)
  }, [isTracking, currentSession])

  // Socket event listeners
  useEffect(() => {
    if (!socket || !isConnected) return

    const handleTrackingStarted = (data) => {
      setCurrentSession(data)
      setIsTracking(true)
      queryClient.invalidateQueries('activeSession')
    }

    const handleTrackingStopped = () => {
      setCurrentSession(null)
      setIsTracking(false)
      setElapsedTime(0)
      queryClient.invalidateQueries('activeSession')
      queryClient.invalidateQueries('sessions')
    }

    const handleTrackingPaused = () => {
      setIsTracking(false)
    }

    const handleTrackingResumed = () => {
      setIsTracking(true)
    }

    socket.on('tracking-started', handleTrackingStarted)
    socket.on('tracking-stopped', handleTrackingStopped)
    socket.on('tracking-paused', handleTrackingPaused)
    socket.on('tracking-resumed', handleTrackingResumed)

    return () => {
      socket.off('tracking-started', handleTrackingStarted)
      socket.off('tracking-stopped', handleTrackingStopped)
      socket.off('tracking-paused', handleTrackingPaused)
      socket.off('tracking-resumed', handleTrackingResumed)
    }
  }, [socket, isConnected, queryClient])

  // Format time display
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const handleStartTracking = () => {
    startTrackingMutation.mutate({
      projectId: selectedProject || null,
      description,
      trackingType: 'manual'
    })
  }

  const handleStopTracking = () => {
    stopTrackingMutation.mutate()
  }

  const handlePauseTracking = () => {
    if (isTracking) {
      pauseTrackingMutation.mutate()
    } else {
      resumeTrackingMutation.mutate()
    }
  }

  const handleManualEntry = (e) => {
    e.preventDefault()
    const formData = new FormData(e.target)

    const startTime = new Date(`${formData.get('date')}T${formData.get('startTime')}`)
    const endTime = new Date(`${formData.get('date')}T${formData.get('endTime')}`)
    const duration = Math.floor((endTime - startTime) / 1000)

    manualEntryMutation.mutate({
      projectId: formData.get('projectId') || null,
      description: formData.get('description'),
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration,
      trackingType: 'manual'
    })
  }

  if (activeLoading) {
    return <Loading fullScreen />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Time Tracking
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Track your time across projects and tasks
          </p>
        </div>

        <div className="mt-4 sm:mt-0 flex gap-3">
          <button
            onClick={() => setShowManualEntry(true)}
            className="btn btn-secondary flex items-center"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Manual Entry
          </button>
        </div>
      </div>

      {/* Active Timer */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="card"
      >
        <div className="card-body">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            {/* Timer Display */}
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-4xl font-mono font-bold text-gray-900 dark:text-gray-100">
                  {formatTime(elapsedTime)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {isTracking ? 'Tracking' : currentSession ? 'Paused' : 'Not tracking'}
                </div>
              </div>

              {/* Session Info */}
              {currentSession && (
                <div className="flex-1 min-w-0">
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <FolderIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {projects?.find(p => p.id === currentSession.projectId)?.name || 'No Project'}
                      </span>
                    </div>
                    {currentSession.description && (
                      <div className="flex items-center">
                        <TagIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                          {currentSession.description}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="mt-4 lg:mt-0 flex items-center space-x-3">
              {!currentSession ? (
                // Start tracking controls
                <div className="flex items-center space-x-3">
                  <select
                    value={selectedProject}
                    onChange={(e) => setSelectedProject(e.target.value)}
                    className="form-select text-sm"
                  >
                    <option value="">No Project</option>
                    {projects?.map(project => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>

                  <input
                    type="text"
                    placeholder="What are you working on?"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="form-input text-sm"
                    style={{ minWidth: '200px' }}
                  />

                  <button
                    onClick={handleStartTracking}
                    disabled={startTrackingMutation.isLoading}
                    className="btn btn-primary flex items-center"
                  >
                    <PlayIcon className="h-4 w-4 mr-2" />
                    Start
                  </button>
                </div>
              ) : (
                // Active session controls
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handlePauseTracking}
                    disabled={pauseTrackingMutation.isLoading || resumeTrackingMutation.isLoading}
                    className="btn btn-secondary flex items-center"
                  >
                    {isTracking ? (
                      <>
                        <PauseIcon className="h-4 w-4 mr-2" />
                        Pause
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-4 w-4 mr-2" />
                        Resume
                      </>
                    )}
                  </button>

                  <button
                    onClick={handleStopTracking}
                    disabled={stopTrackingMutation.isLoading}
                    className="btn btn-danger flex items-center"
                  >
                    <StopIcon className="h-4 w-4 mr-2" />
                    Stop
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="form-select text-sm"
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="all">All Time</option>
          </select>

          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="form-select text-sm"
          >
            <option value="all">All Projects</option>
            {projects?.map(project => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
        </div>

        <div className="text-sm text-gray-500 dark:text-gray-400">
          {sessionsData?.length || 0} sessions
        </div>
      </div>

      {/* Sessions List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="card"
      >
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Recent Sessions
          </h3>
        </div>
        <div className="card-body p-0">
          {sessionsLoading ? (
            <div className="p-6">
              <Loading />
            </div>
          ) : sessionsData?.length > 0 ? (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {sessionsData.map((session) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-6 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          session.isActive ? 'bg-green-400' : 'bg-gray-400'
                        }`} />

                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            {session.project && (
                              <span
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                style={{
                                  backgroundColor: session.project.color + '20',
                                  color: session.project.color
                                }}
                              >
                                {session.project.name}
                              </span>
                            )}

                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {new Date(session.startTime).toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                              {session.endTime && (
                                <> - {new Date(session.endTime).toLocaleTimeString([], {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}</>
                              )}
                            </span>
                          </div>

                          {session.description && (
                            <p className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                              {session.description}
                            </p>
                          )}

                          <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                            <span>{session.trackingType}</span>
                            {session.applicationName && (
                              <span>• {session.applicationName}</span>
                            )}
                            {session.websiteDomain && (
                              <span>• {session.websiteDomain}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                          {session.duration ? formatDuration(session.duration) : 'Active'}
                        </div>
                        {session.billable && (
                          <div className="text-xs text-green-600">Billable</div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteSessionMutation.mutate(session.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center">
              <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                No sessions found
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Start tracking time to see your sessions here.
              </p>
            </div>
          )}
        </div>
      </motion.div>

      {/* Manual Entry Modal */}
      <AnimatePresence>
        {showManualEntry && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowManualEntry(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Add Manual Entry
                </h3>

                <form onSubmit={handleManualEntry} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Project
                    </label>
                    <select name="projectId" className="form-select w-full">
                      <option value="">No Project</option>
                      {projects?.map(project => (
                        <option key={project.id} value={project.id}>
                          {project.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Description
                    </label>
                    <input
                      type="text"
                      name="description"
                      placeholder="What did you work on?"
                      className="form-input w-full"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Date
                    </label>
                    <input
                      type="date"
                      name="date"
                      defaultValue={new Date().toISOString().split('T')[0]}
                      className="form-input w-full"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Start Time
                      </label>
                      <input
                        type="time"
                        name="startTime"
                        className="form-input w-full"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        End Time
                      </label>
                      <input
                        type="time"
                        name="endTime"
                        className="form-input w-full"
                        required
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowManualEntry(false)}
                      className="btn btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={manualEntryMutation.isLoading}
                      className="btn btn-primary"
                    >
                      {manualEntryMutation.isLoading ? 'Adding...' : 'Add Entry'}
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export default TimeTracking
