/**
 * TimeWarp Browser Extension - Background Service Worker
 * Handles time tracking, focus mode, and API communication
 */

// Configuration
const API_BASE_URL = 'http://localhost:3001/api';
let currentSession = null;
let focusSession = null;
let isTracking = false;
let lastActiveTab = null;
let sessionStartTime = null;

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('TimeWarp extension installed');
  
  // Set up default settings
  chrome.storage.sync.set({
    trackingEnabled: true,
    focusModeEnabled: false,
    idleThreshold: 300, // 5 minutes
    apiUrl: API_BASE_URL,
    autoTrack: true,
    notifications: true
  });
  
  // Create context menu
  chrome.contextMenus.create({
    id: 'start-tracking',
    title: 'Start TimeWarp Tracking',
    contexts: ['page']
  });
  
  chrome.contextMenus.create({
    id: 'start-focus-mode',
    title: 'Start Focus Mode',
    contexts: ['page']
  });
});

// Handle tab changes for time tracking
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  if (!isTracking) return;
  
  try {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    await handleTabChange(tab);
  } catch (error) {
    console.error('Error handling tab activation:', error);
  }
});

chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (!isTracking || changeInfo.status !== 'complete') return;
  
  try {
    if (tab.active) {
      await handleTabChange(tab);
    }
  } catch (error) {
    console.error('Error handling tab update:', error);
  }
});

// Handle window focus changes
chrome.windows.onFocusChanged.addListener(async (windowId) => {
  if (!isTracking || windowId === chrome.windows.WINDOW_ID_NONE) return;
  
  try {
    const tabs = await chrome.tabs.query({ active: true, windowId });
    if (tabs.length > 0) {
      await handleTabChange(tabs[0]);
    }
  } catch (error) {
    console.error('Error handling window focus:', error);
  }
});

// Handle idle state changes
chrome.idle.onStateChanged.addListener(async (state) => {
  console.log('Idle state changed:', state);
  
  if (state === 'idle' && currentSession) {
    await pauseCurrentSession();
  } else if (state === 'active' && currentSession && currentSession.isPaused) {
    await resumeCurrentSession();
  }
});

// Handle alarms (for session timers)
chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'focus-session-end') {
    await endFocusSession();
  } else if (alarm.name === 'session-sync') {
    await syncCurrentSession();
  }
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  switch (info.menuItemId) {
    case 'start-tracking':
      await startTracking(tab);
      break;
    case 'start-focus-mode':
      await startFocusMode();
      break;
  }
});

// Handle keyboard shortcuts
chrome.commands.onCommand.addListener(async (command) => {
  switch (command) {
    case 'start-focus-mode':
      await startFocusMode();
      break;
    case 'quick-timer':
      await startQuickTimer();
      break;
  }
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  handleMessage(request, sender, sendResponse);
  return true; // Keep message channel open for async response
});

async function handleMessage(request, sender, sendResponse) {
  try {
    switch (request.action) {
      case 'getStatus':
        sendResponse({
          isTracking,
          currentSession,
          focusSession,
          lastActiveTab
        });
        break;
        
      case 'startTracking':
        await startTracking();
        sendResponse({ success: true });
        break;
        
      case 'stopTracking':
        await stopTracking();
        sendResponse({ success: true });
        break;
        
      case 'startFocusMode':
        await startFocusMode(request.options);
        sendResponse({ success: true });
        break;
        
      case 'stopFocusMode':
        await stopFocusMode();
        sendResponse({ success: true });
        break;
        
      case 'checkBlocked':
        const isBlocked = await checkIfBlocked(request.url);
        sendResponse({ blocked: isBlocked });
        break;
        
      case 'recordDistraction':
        await recordDistraction(request.data);
        sendResponse({ success: true });
        break;
        
      default:
        sendResponse({ error: 'Unknown action' });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    sendResponse({ error: error.message });
  }
}

async function handleTabChange(tab) {
  if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
    return;
  }
  
  // End previous session if exists
  if (currentSession && lastActiveTab && lastActiveTab.url !== tab.url) {
    await endCurrentSession();
  }
  
  // Start new session for this tab
  if (!currentSession || lastActiveTab?.url !== tab.url) {
    await startWebsiteSession(tab);
  }
  
  lastActiveTab = tab;
}

async function startWebsiteSession(tab) {
  try {
    const settings = await chrome.storage.sync.get(['apiUrl', 'authToken']);
    
    if (!settings.authToken) {
      console.log('No auth token found, skipping API call');
      return;
    }
    
    const sessionData = {
      websiteUrl: tab.url,
      windowTitle: tab.title,
      trackingType: 'browser',
      startTime: new Date().toISOString()
    };
    
    const response = await fetch(`${settings.apiUrl}/tracking/website`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${settings.authToken}`
      },
      body: JSON.stringify(sessionData)
    });
    
    if (response.ok) {
      currentSession = await response.json();
      sessionStartTime = Date.now();
      
      // Set up periodic sync
      chrome.alarms.create('session-sync', { periodInMinutes: 1 });
      
      console.log('Started website session:', currentSession);
    }
  } catch (error) {
    console.error('Error starting website session:', error);
  }
}

async function endCurrentSession() {
  if (!currentSession) return;
  
  try {
    const settings = await chrome.storage.sync.get(['apiUrl', 'authToken']);
    const duration = Math.floor((Date.now() - sessionStartTime) / 1000);
    
    const response = await fetch(`${settings.apiUrl}/tracking/website`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${settings.authToken}`
      },
      body: JSON.stringify({
        ...currentSession.data.session,
        endTime: new Date().toISOString(),
        duration
      })
    });
    
    if (response.ok) {
      console.log('Ended website session');
    }
  } catch (error) {
    console.error('Error ending website session:', error);
  } finally {
    currentSession = null;
    sessionStartTime = null;
    chrome.alarms.clear('session-sync');
  }
}

async function startTracking(tab) {
  isTracking = true;
  
  // Update icon to show tracking is active
  chrome.action.setIcon({ path: 'icons/icon-active-32.png' });
  chrome.action.setBadgeText({ text: '●' });
  chrome.action.setBadgeBackgroundColor({ color: '#22c55e' });
  
  if (tab) {
    await handleTabChange(tab);
  }
  
  // Show notification
  const settings = await chrome.storage.sync.get(['notifications']);
  if (settings.notifications) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon-48.png',
      title: 'TimeWarp Tracking Started',
      message: 'Your web activity is now being tracked.'
    });
  }
}

async function stopTracking() {
  isTracking = false;
  
  // End current session
  await endCurrentSession();
  
  // Update icon
  chrome.action.setIcon({ path: 'icons/icon-32.png' });
  chrome.action.setBadgeText({ text: '' });
  
  // Show notification
  const settings = await chrome.storage.sync.get(['notifications']);
  if (settings.notifications) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon-48.png',
      title: 'TimeWarp Tracking Stopped',
      message: 'Web activity tracking has been paused.'
    });
  }
}

async function startFocusMode(options = {}) {
  try {
    const settings = await chrome.storage.sync.get(['apiUrl', 'authToken']);
    
    const focusData = {
      name: 'Browser Focus Session',
      type: 'custom',
      duration: options.duration || 25,
      blockingEnabled: true,
      blockedWebsites: options.blockedWebsites || [
        'facebook.com', 'twitter.com', 'instagram.com', 
        'youtube.com', 'reddit.com', 'tiktok.com'
      ],
      blockingMode: 'blacklist'
    };
    
    const response = await fetch(`${settings.apiUrl}/focus/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${settings.authToken}`
      },
      body: JSON.stringify(focusData)
    });
    
    if (response.ok) {
      focusSession = await response.json();
      
      // Set alarm for session end
      chrome.alarms.create('focus-session-end', { 
        delayInMinutes: focusData.duration 
      });
      
      // Update icon
      chrome.action.setIcon({ path: 'icons/icon-focus-32.png' });
      chrome.action.setBadgeText({ text: 'F' });
      chrome.action.setBadgeBackgroundColor({ color: '#ef4444' });
      
      // Notify content scripts
      chrome.tabs.query({}, (tabs) => {
        tabs.forEach(tab => {
          chrome.tabs.sendMessage(tab.id, {
            action: 'focusModeStarted',
            session: focusSession
          }).catch(() => {}); // Ignore errors for inactive tabs
        });
      });
      
      console.log('Focus mode started:', focusSession);
    }
  } catch (error) {
    console.error('Error starting focus mode:', error);
  }
}

async function endFocusSession() {
  if (!focusSession) return;
  
  try {
    const settings = await chrome.storage.sync.get(['apiUrl', 'authToken']);
    
    const response = await fetch(`${settings.apiUrl}/focus/stop`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${settings.authToken}`
      }
    });
    
    if (response.ok) {
      // Update icon
      chrome.action.setIcon({ path: 'icons/icon-32.png' });
      chrome.action.setBadgeText({ text: '' });
      
      // Notify content scripts
      chrome.tabs.query({}, (tabs) => {
        tabs.forEach(tab => {
          chrome.tabs.sendMessage(tab.id, {
            action: 'focusModeEnded'
          }).catch(() => {});
        });
      });
      
      // Show completion notification
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'Focus Session Complete!',
        message: 'Great job! Your focus session has ended.'
      });
      
      console.log('Focus session ended');
    }
  } catch (error) {
    console.error('Error ending focus session:', error);
  } finally {
    focusSession = null;
    chrome.alarms.clear('focus-session-end');
  }
}

async function checkIfBlocked(url) {
  if (!focusSession || !focusSession.data.session.blockingEnabled) {
    return false;
  }
  
  try {
    const domain = new URL(url).hostname.toLowerCase();
    const blockedSites = focusSession.data.session.blockedWebsites || [];
    
    return blockedSites.some(blocked => 
      domain.includes(blocked.toLowerCase()) || 
      blocked.toLowerCase().includes(domain)
    );
  } catch {
    return false;
  }
}

async function recordDistraction(data) {
  if (!focusSession) return;
  
  try {
    const settings = await chrome.storage.sync.get(['apiUrl', 'authToken']);
    
    await fetch(`${settings.apiUrl}/focus/distraction`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${settings.authToken}`
      },
      body: JSON.stringify(data)
    });
  } catch (error) {
    console.error('Error recording distraction:', error);
  }
}

async function syncCurrentSession() {
  if (!currentSession || !sessionStartTime) return;
  
  try {
    const settings = await chrome.storage.sync.get(['apiUrl', 'authToken']);
    const duration = Math.floor((Date.now() - sessionStartTime) / 1000);
    
    // Update session with current duration
    await fetch(`${settings.apiUrl}/tracking/sessions/${currentSession.data.session.id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${settings.authToken}`
      },
      body: JSON.stringify({ duration })
    });
  } catch (error) {
    console.error('Error syncing session:', error);
  }
}
