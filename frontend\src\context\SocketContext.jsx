import React, { createContext, useContext, useEffect, useState, useRef } from 'react'
import { io } from 'socket.io-client'
import { useAuth } from './AuthContext'
import { tokenService } from '@services/tokenService'

// Create context
const SocketContext = createContext()

// Socket provider component
export function SocketProvider({ children }) {
  const [socket, setSocket] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState(null)
  const { user, isAuthenticated } = useAuth()
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  useEffect(() => {
    if (isAuthenticated && user) {
      // Initialize socket connection
      const newSocket = io(process.env.VITE_BACKEND_URL || 'http://localhost:3001', {
        auth: {
          token: tokenService.getToken()
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: maxReconnectAttempts,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000
      })

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('Socket connected:', newSocket.id)
        setIsConnected(true)
        setConnectionError(null)
        reconnectAttempts.current = 0
        
        // Join user-specific room
        newSocket.emit('join-user-room', user.id)
      })

      newSocket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason)
        setIsConnected(false)
        
        if (reason === 'io server disconnect') {
          // Server disconnected, need to reconnect manually
          newSocket.connect()
        }
      })

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error)
        setConnectionError(error.message)
        reconnectAttempts.current += 1
        
        if (reconnectAttempts.current >= maxReconnectAttempts) {
          setConnectionError('Failed to connect after multiple attempts')
        }
      })

      newSocket.on('reconnect', (attemptNumber) => {
        console.log('Socket reconnected after', attemptNumber, 'attempts')
        setIsConnected(true)
        setConnectionError(null)
        reconnectAttempts.current = 0
      })

      newSocket.on('reconnect_error', (error) => {
        console.error('Socket reconnection error:', error)
        setConnectionError('Reconnection failed')
      })

      newSocket.on('reconnect_failed', () => {
        console.error('Socket reconnection failed after max attempts')
        setConnectionError('Connection failed. Please refresh the page.')
      })

      // Set socket instance
      setSocket(newSocket)

      // Cleanup on unmount or user change
      return () => {
        console.log('Cleaning up socket connection')
        newSocket.disconnect()
        setSocket(null)
        setIsConnected(false)
        setConnectionError(null)
      }
    } else {
      // User not authenticated, cleanup socket
      if (socket) {
        socket.disconnect()
        setSocket(null)
        setIsConnected(false)
        setConnectionError(null)
      }
    }
  }, [isAuthenticated, user])

  // Socket utility functions
  const emit = (event, data) => {
    if (socket && isConnected) {
      socket.emit(event, data)
    } else {
      console.warn('Socket not connected, cannot emit event:', event)
    }
  }

  const on = (event, callback) => {
    if (socket) {
      socket.on(event, callback)
      
      // Return cleanup function
      return () => {
        socket.off(event, callback)
      }
    }
  }

  const off = (event, callback) => {
    if (socket) {
      socket.off(event, callback)
    }
  }

  const joinRoom = (roomId) => {
    emit('join-room', roomId)
  }

  const leaveRoom = (roomId) => {
    emit('leave-room', roomId)
  }

  // Time tracking events
  const startTracking = (data) => {
    emit('start-tracking', data)
  }

  const stopTracking = (data) => {
    emit('stop-tracking', data)
  }

  // Focus mode events
  const startFocusMode = (data) => {
    emit('focus-mode-start', data)
  }

  const endFocusMode = (data) => {
    emit('focus-mode-end', data)
  }

  // Team collaboration events
  const joinTeamRoom = (teamId) => {
    emit('join-team-room', teamId)
  }

  const sendTeamUpdate = (data) => {
    emit('team-update', data)
  }

  const value = {
    socket,
    isConnected,
    connectionError,
    
    // Utility functions
    emit,
    on,
    off,
    joinRoom,
    leaveRoom,
    
    // Feature-specific functions
    startTracking,
    stopTracking,
    startFocusMode,
    endFocusMode,
    joinTeamRoom,
    sendTeamUpdate
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}

// Custom hook to use socket context
export function useSocket() {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}

export default SocketContext
