import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '@context/AuthContext'
import { useTheme } from '@context/ThemeContext'

// Layout Components
import Layout from '@components/Layout/Layout'
import AuthLayout from '@components/Layout/AuthLayout'

// Page Components
import Dashboard from '@pages/Dashboard'
import TimeTracking from '@pages/TimeTracking'
import Tasks from '@pages/Tasks'
import Calendar from '@pages/Calendar'
import Analytics from '@pages/Analytics'
import Focus from '@pages/Focus'
import Teams from '@pages/Teams'
import Settings from '@pages/Settings'
import Profile from '@pages/Profile'

// Auth Pages
import Login from '@pages/Auth/Login'
import Register from '@pages/Auth/Register'
import ForgotPassword from '@pages/Auth/ForgotPassword'
import ResetPassword from '@pages/Auth/ResetPassword'

// Other Pages
import Landing from '@pages/Landing'
import NotFound from '@pages/NotFound'
import Loading from '@components/Loading'

function App() {
  const { user, loading } = useAuth()
  const { theme } = useTheme()

  // Show loading spinner while checking authentication
  if (loading) {
    return <Loading />
  }

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'dark' : ''}`}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={user ? <Navigate to="/dashboard" replace /> : <Landing />} />
          
          {/* Auth Routes */}
          <Route path="/auth" element={user ? <Navigate to="/dashboard" replace /> : <AuthLayout />}>
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
            <Route path="forgot-password" element={<ForgotPassword />} />
            <Route path="reset-password/:token" element={<ResetPassword />} />
            <Route index element={<Navigate to="login" replace />} />
          </Route>

          {/* Protected Routes */}
          <Route path="/" element={user ? <Layout /> : <Navigate to="/auth/login" replace />}>
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="tracking" element={<TimeTracking />} />
            <Route path="tasks" element={<Tasks />} />
            <Route path="calendar" element={<Calendar />} />
            <Route path="analytics" element={<Analytics />} />
            <Route path="focus" element={<Focus />} />
            <Route path="teams" element={<Teams />} />
            <Route path="settings" element={<Settings />} />
            <Route path="profile" element={<Profile />} />
          </Route>

          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </div>
    </div>
  )
}

export default App
