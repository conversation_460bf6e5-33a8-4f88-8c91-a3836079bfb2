/**
 * Time Tracking Controller
 * Handles time tracking sessions and related operations
 */

const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const TimeSession = require('../models/TimeSession');
const Project = require('../models/Project');
const { Op } = require('sequelize');
const moment = require('moment');

/**
 * Get user's time tracking sessions with filtering and pagination
 */
const getSessions = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 50,
      startDate,
      endDate,
      projectId,
      taskId,
      trackingType,
      isActive,
      productivity
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { userId: req.user.id };

    // Apply filters
    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime[Op.gte] = new Date(startDate);
      if (endDate) where.startTime[Op.lte] = new Date(endDate);
    }

    if (projectId) where.projectId = projectId;
    if (taskId) where.taskId = taskId;
    if (trackingType) where.trackingType = trackingType;
    if (isActive !== undefined) where.isActive = isActive === 'true';
    if (productivity) where.productivity = productivity;

    const { count, rows: sessions } = await TimeSession.findAndCountAll({
      where,
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'color', 'isBillable']
        }
      ],
      order: [['startTime', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    res.status(200).json({
      status: 'success',
      results: sessions.length,
      totalCount: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      data: { sessions }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new time tracking session
 */
const createSession = async (req, res, next) => {
  try {
    const {
      projectId,
      taskId,
      description,
      trackingType = 'manual',
      applicationName,
      windowTitle,
      websiteUrl,
      category,
      tags = [],
      billable = false,
      hourlyRate
    } = req.body;

    // Check if user has an active session
    const activeSession = await TimeSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      }
    });

    if (activeSession) {
      return next(new AppError('You already have an active tracking session. Please stop it first.', 400));
    }

    // Validate project ownership if provided
    if (projectId) {
      const project = await Project.findOne({
        where: {
          id: projectId,
          userId: req.user.id
        }
      });

      if (!project) {
        return next(new AppError('Project not found or access denied', 404));
      }
    }

    const session = await TimeSession.create({
      userId: req.user.id,
      projectId,
      taskId,
      description,
      trackingType,
      applicationName,
      windowTitle,
      websiteUrl,
      category,
      tags,
      billable,
      hourlyRate,
      deviceInfo: {
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        timestamp: new Date()
      }
    });

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('tracking-started', {
        sessionId: session.id,
        startTime: session.startTime,
        projectId: session.projectId,
        description: session.description
      });
    }

    logger.logUserAction('time_tracking_started', req.user.id, {
      sessionId: session.id,
      projectId,
      trackingType
    });

    res.status(201).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get a specific time tracking session
 */
const getSession = async (req, res, next) => {
  try {
    const session = await TimeSession.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      },
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'color', 'isBillable', 'defaultHourlyRate']
        }
      ]
    });

    if (!session) {
      return next(new AppError('Session not found', 404));
    }

    res.status(200).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update a time tracking session
 */
const updateSession = async (req, res, next) => {
  try {
    const session = await TimeSession.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });

    if (!session) {
      return next(new AppError('Session not found', 404));
    }

    // Don't allow updating active sessions' core timing data
    if (session.isActive) {
      const allowedFields = ['description', 'projectId', 'taskId', 'category', 'tags', 'billable', 'hourlyRate'];
      const updateData = {};

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      });

      await session.update(updateData);
    } else {
      // Allow full updates for completed sessions
      await session.update(req.body);
    }

    res.status(200).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a time tracking session
 */
const deleteSession = async (req, res, next) => {
  try {
    const session = await TimeSession.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });

    if (!session) {
      return next(new AppError('Session not found', 404));
    }

    await session.destroy();

    logger.logUserAction('time_session_deleted', req.user.id, {
      sessionId: session.id,
      duration: session.duration
    });

    res.status(204).json({
      status: 'success',
      data: null
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current active session
 */
const getActiveSession = async (req, res, next) => {
  try {
    const activeSession = await TimeSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      },
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'color', 'isBillable']
        }
      ]
    });

    res.status(200).json({
      status: 'success',
      data: { session: activeSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Start time tracking (alias for createSession)
 */
const startTracking = async (req, res, next) => {
  return createSession(req, res, next);
};

/**
 * Stop active time tracking session
 */
const stopTracking = async (req, res, next) => {
  try {
    const activeSession = await TimeSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true
      }
    });

    if (!activeSession) {
      return next(new AppError('No active tracking session found', 404));
    }

    await activeSession.stop();

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('tracking-stopped', {
        sessionId: activeSession.id,
        endTime: activeSession.endTime,
        duration: activeSession.duration
      });
    }

    logger.logUserAction('time_tracking_stopped', req.user.id, {
      sessionId: activeSession.id,
      duration: activeSession.duration
    });

    res.status(200).json({
      status: 'success',
      data: { session: activeSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Pause active time tracking session
 */
const pauseTracking = async (req, res, next) => {
  try {
    const activeSession = await TimeSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true,
        isPaused: false
      }
    });

    if (!activeSession) {
      return next(new AppError('No active tracking session found', 404));
    }

    await activeSession.pause();

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('tracking-paused', {
        sessionId: activeSession.id
      });
    }

    res.status(200).json({
      status: 'success',
      data: { session: activeSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Resume paused time tracking session
 */
const resumeTracking = async (req, res, next) => {
  try {
    const pausedSession = await TimeSession.findOne({
      where: {
        userId: req.user.id,
        isActive: true,
        isPaused: true
      }
    });

    if (!pausedSession) {
      return next(new AppError('No paused tracking session found', 404));
    }

    await pausedSession.resume();

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`user-${req.user.id}`).emit('tracking-resumed', {
        sessionId: pausedSession.id
      });
    }

    res.status(200).json({
      status: 'success',
      data: { session: pausedSession }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get comprehensive time tracking statistics
 */
const getStats = async (req, res, next) => {
  try {
    const { startDate, endDate } = req.query;
    const where = {
      userId: req.user.id,
      endTime: { [Op.not]: null }
    };

    // Apply date filters
    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime[Op.gte] = new Date(startDate);
      if (endDate) where.startTime[Op.lte] = new Date(endDate);
    }

    const sessions = await TimeSession.findAll({ where });

    // Calculate statistics
    const totalSessions = sessions.length;
    const totalTime = sessions.reduce((acc, session) => acc + (session.duration || 0), 0);
    const totalBillableTime = sessions
      .filter(s => s.billable)
      .reduce((acc, session) => acc + (session.duration || 0), 0);

    const totalRevenue = sessions
      .filter(s => s.billable && s.hourlyRate)
      .reduce((acc, session) => {
        const hours = (session.duration || 0) / 3600;
        return acc + (hours * parseFloat(session.hourlyRate || 0));
      }, 0);

    // Application usage statistics
    const appUsage = {};
    sessions.forEach(session => {
      if (session.applicationName) {
        appUsage[session.applicationName] = (appUsage[session.applicationName] || 0) + (session.duration || 0);
      }
    });

    const mostUsedApplication = Object.keys(appUsage).length > 0
      ? Object.keys(appUsage).reduce((a, b) => appUsage[a] > appUsage[b] ? a : b)
      : null;

    // Website usage statistics
    const websiteUsage = {};
    sessions.forEach(session => {
      if (session.websiteDomain) {
        websiteUsage[session.websiteDomain] = (websiteUsage[session.websiteDomain] || 0) + (session.duration || 0);
      }
    });

    // Productivity distribution
    const productivityStats = {
      very_productive: 0,
      productive: 0,
      neutral: 0,
      distracting: 0,
      very_distracting: 0
    };

    sessions.forEach(session => {
      if (session.productivity) {
        productivityStats[session.productivity] += (session.duration || 0);
      }
    });

    const stats = {
      totalSessions,
      totalTime,
      totalBillableTime,
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      averageSessionLength: totalSessions > 0 ? Math.round(totalTime / totalSessions) : 0,
      mostUsedApplication,
      applicationUsage: Object.entries(appUsage)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([name, time]) => ({ name, time })),
      websiteUsage: Object.entries(websiteUsage)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([domain, time]) => ({ domain, time })),
      productivityDistribution: productivityStats,
      averageProductivityScore: sessions.length > 0
        ? sessions.reduce((acc, session) => acc + session.getProductivityScore(), 0) / sessions.length
        : 50
    };

    res.status(200).json({
      status: 'success',
      data: { stats }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get daily time tracking statistics
 */
const getDailyStats = async (req, res, next) => {
  try {
    const { date = moment().format('YYYY-MM-DD') } = req.query;
    const startOfDay = moment(date).startOf('day').toDate();
    const endOfDay = moment(date).endOf('day').toDate();

    const sessions = await TimeSession.findAll({
      where: {
        userId: req.user.id,
        startTime: {
          [Op.between]: [startOfDay, endOfDay]
        }
      },
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'color']
        }
      ],
      order: [['startTime', 'ASC']]
    });

    // Group by hour
    const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      totalTime: 0,
      sessions: 0,
      productivity: 0
    }));

    sessions.forEach(session => {
      const hour = moment(session.startTime).hour();
      hourlyData[hour].totalTime += session.duration || 0;
      hourlyData[hour].sessions += 1;
      hourlyData[hour].productivity += session.getProductivityScore();
    });

    // Calculate average productivity per hour
    hourlyData.forEach(data => {
      if (data.sessions > 0) {
        data.productivity = Math.round(data.productivity / data.sessions);
      }
    });

    const totalTime = sessions.reduce((acc, session) => acc + (session.duration || 0), 0);
    const completedSessions = sessions.filter(s => s.endTime).length;

    res.status(200).json({
      status: 'success',
      data: {
        date,
        totalTime,
        totalSessions: sessions.length,
        completedSessions,
        hourlyData,
        sessions: sessions.map(session => ({
          id: session.id,
          startTime: session.startTime,
          endTime: session.endTime,
          duration: session.duration,
          project: session.project,
          applicationName: session.applicationName,
          productivity: session.productivity,
          isActive: session.isActive
        }))
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get weekly time tracking statistics
 */
const getWeeklyStats = async (req, res, next) => {
  try {
    const { week = moment().format('YYYY-[W]WW') } = req.query;
    const startOfWeek = moment(week, 'YYYY-[W]WW').startOf('isoWeek').toDate();
    const endOfWeek = moment(week, 'YYYY-[W]WW').endOf('isoWeek').toDate();

    const sessions = await TimeSession.findAll({
      where: {
        userId: req.user.id,
        startTime: {
          [Op.between]: [startOfWeek, endOfWeek]
        },
        endTime: { [Op.not]: null }
      },
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'color']
        }
      ]
    });

    // Group by day
    const dailyData = Array.from({ length: 7 }, (_, dayIndex) => {
      const date = moment(startOfWeek).add(dayIndex, 'days');
      return {
        date: date.format('YYYY-MM-DD'),
        dayName: date.format('dddd'),
        totalTime: 0,
        sessions: 0,
        productivity: 0
      };
    });

    sessions.forEach(session => {
      const dayIndex = moment(session.startTime).diff(moment(startOfWeek), 'days');
      if (dayIndex >= 0 && dayIndex < 7) {
        dailyData[dayIndex].totalTime += session.duration || 0;
        dailyData[dayIndex].sessions += 1;
        dailyData[dayIndex].productivity += session.getProductivityScore();
      }
    });

    // Calculate average productivity per day
    dailyData.forEach(data => {
      if (data.sessions > 0) {
        data.productivity = Math.round(data.productivity / data.sessions);
      }
    });

    const totalTime = sessions.reduce((acc, session) => acc + (session.duration || 0), 0);

    res.status(200).json({
      status: 'success',
      data: {
        week,
        startDate: moment(startOfWeek).format('YYYY-MM-DD'),
        endDate: moment(endOfWeek).format('YYYY-MM-DD'),
        totalTime,
        totalSessions: sessions.length,
        dailyData
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get monthly time tracking statistics
 */
const getMonthlyStats = async (req, res, next) => {
  try {
    const { month = moment().format('YYYY-MM') } = req.query;
    const startOfMonth = moment(month, 'YYYY-MM').startOf('month').toDate();
    const endOfMonth = moment(month, 'YYYY-MM').endOf('month').toDate();

    const sessions = await TimeSession.findAll({
      where: {
        userId: req.user.id,
        startTime: {
          [Op.between]: [startOfMonth, endOfMonth]
        },
        endTime: { [Op.not]: null }
      },
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'color']
        }
      ]
    });

    // Group by week
    const weeksInMonth = Math.ceil(moment(endOfMonth).diff(moment(startOfMonth), 'days') / 7);
    const weeklyData = Array.from({ length: weeksInMonth }, (_, weekIndex) => {
      const weekStart = moment(startOfMonth).add(weekIndex * 7, 'days');
      return {
        week: weekIndex + 1,
        startDate: weekStart.format('YYYY-MM-DD'),
        endDate: weekStart.add(6, 'days').format('YYYY-MM-DD'),
        totalTime: 0,
        sessions: 0,
        productivity: 0
      };
    });

    sessions.forEach(session => {
      const weekIndex = Math.floor(moment(session.startTime).diff(moment(startOfMonth), 'days') / 7);
      if (weekIndex >= 0 && weekIndex < weeksInMonth) {
        weeklyData[weekIndex].totalTime += session.duration || 0;
        weeklyData[weekIndex].sessions += 1;
        weeklyData[weekIndex].productivity += session.getProductivityScore();
      }
    });

    // Calculate average productivity per week
    weeklyData.forEach(data => {
      if (data.sessions > 0) {
        data.productivity = Math.round(data.productivity / data.sessions);
      }
    });

    const totalTime = sessions.reduce((acc, session) => acc + (session.duration || 0), 0);

    res.status(200).json({
      status: 'success',
      data: {
        month,
        totalTime,
        totalSessions: sessions.length,
        weeklyData
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get tracked applications with usage statistics
 */
const getTrackedApplications = async (req, res, next) => {
  try {
    const { startDate, endDate, limit = 20 } = req.query;
    const where = {
      userId: req.user.id,
      applicationName: { [Op.not]: null },
      endTime: { [Op.not]: null }
    };

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime[Op.gte] = new Date(startDate);
      if (endDate) where.startTime[Op.lte] = new Date(endDate);
    }

    const sessions = await TimeSession.findAll({ where });

    // Group by application
    const appStats = {};
    sessions.forEach(session => {
      const app = session.applicationName;
      if (!appStats[app]) {
        appStats[app] = {
          name: app,
          totalTime: 0,
          sessions: 0,
          productivity: 0,
          lastUsed: session.startTime
        };
      }

      appStats[app].totalTime += session.duration || 0;
      appStats[app].sessions += 1;
      appStats[app].productivity += session.getProductivityScore();

      if (moment(session.startTime).isAfter(appStats[app].lastUsed)) {
        appStats[app].lastUsed = session.startTime;
      }
    });

    // Calculate average productivity and sort by usage
    const applications = Object.values(appStats)
      .map(app => ({
        ...app,
        productivity: app.sessions > 0 ? Math.round(app.productivity / app.sessions) : 0
      }))
      .sort((a, b) => b.totalTime - a.totalTime)
      .slice(0, parseInt(limit));

    res.status(200).json({
      status: 'success',
      data: { applications }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Track application usage (for desktop app integration)
 */
const trackApplication = async (req, res, next) => {
  try {
    const { applicationName, windowTitle, duration, startTime, endTime } = req.body;

    if (!applicationName) {
      return next(new AppError('Application name is required', 400));
    }

    // Find or create an active session for this application
    let session = await TimeSession.findOne({
      where: {
        userId: req.user.id,
        applicationName,
        isActive: true,
        trackingType: 'desktop'
      }
    });

    if (!session) {
      // Create new session
      session = await TimeSession.create({
        userId: req.user.id,
        applicationName,
        windowTitle,
        trackingType: 'desktop',
        startTime: startTime ? new Date(startTime) : new Date(),
        endTime: endTime ? new Date(endTime) : null,
        duration: duration || null,
        isActive: !endTime
      });
    } else {
      // Update existing session
      await session.update({
        windowTitle,
        endTime: endTime ? new Date(endTime) : null,
        duration: duration || null,
        isActive: !endTime
      });
    }

    res.status(200).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get tracked websites with usage statistics
 */
const getTrackedWebsites = async (req, res, next) => {
  try {
    const { startDate, endDate, limit = 20 } = req.query;
    const where = {
      userId: req.user.id,
      websiteDomain: { [Op.not]: null },
      endTime: { [Op.not]: null }
    };

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime[Op.gte] = new Date(startDate);
      if (endDate) where.startTime[Op.lte] = new Date(endDate);
    }

    const sessions = await TimeSession.findAll({ where });

    // Group by website domain
    const websiteStats = {};
    sessions.forEach(session => {
      const domain = session.websiteDomain;
      if (!websiteStats[domain]) {
        websiteStats[domain] = {
          domain,
          totalTime: 0,
          sessions: 0,
          productivity: 0,
          lastVisited: session.startTime
        };
      }

      websiteStats[domain].totalTime += session.duration || 0;
      websiteStats[domain].sessions += 1;
      websiteStats[domain].productivity += session.getProductivityScore();

      if (moment(session.startTime).isAfter(websiteStats[domain].lastVisited)) {
        websiteStats[domain].lastVisited = session.startTime;
      }
    });

    // Calculate average productivity and sort by usage
    const websites = Object.values(websiteStats)
      .map(site => ({
        ...site,
        productivity: site.sessions > 0 ? Math.round(site.productivity / site.sessions) : 0
      }))
      .sort((a, b) => b.totalTime - a.totalTime)
      .slice(0, parseInt(limit));

    res.status(200).json({
      status: 'success',
      data: { websites }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Track website usage (for browser extension integration)
 */
const trackWebsite = async (req, res, next) => {
  try {
    const { websiteUrl, windowTitle, duration, startTime, endTime } = req.body;

    if (!websiteUrl) {
      return next(new AppError('Website URL is required', 400));
    }

    // Extract domain from URL
    let domain;
    try {
      domain = new URL(websiteUrl).hostname;
    } catch {
      return next(new AppError('Invalid website URL', 400));
    }

    // Find or create an active session for this website
    let session = await TimeSession.findOne({
      where: {
        userId: req.user.id,
        websiteDomain: domain,
        isActive: true,
        trackingType: 'browser'
      }
    });

    if (!session) {
      // Create new session
      session = await TimeSession.create({
        userId: req.user.id,
        websiteUrl,
        websiteDomain: domain,
        windowTitle,
        trackingType: 'browser',
        startTime: startTime ? new Date(startTime) : new Date(),
        endTime: endTime ? new Date(endTime) : null,
        duration: duration || null,
        isActive: !endTime
      });
    } else {
      // Update existing session
      await session.update({
        websiteUrl,
        windowTitle,
        endTime: endTime ? new Date(endTime) : null,
        duration: duration || null,
        isActive: !endTime
      });
    }

    res.status(200).json({
      status: 'success',
      data: { session }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getSessions,
  createSession,
  getSession,
  updateSession,
  deleteSession,
  getActiveSession,
  startTracking,
  stopTracking,
  pauseTracking,
  resumeTracking,
  getStats,
  getDailyStats,
  getWeeklyStats,
  getMonthlyStats,
  getTrackedApplications,
  trackApplication,
  getTrackedWebsites,
  trackWebsite
};
