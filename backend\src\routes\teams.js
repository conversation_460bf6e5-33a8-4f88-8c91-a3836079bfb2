const express = require('express')
const { body, param, query } = require('express-validator')
const auth = require('../middleware/auth')
const teamsController = require('../controllers/teamsController')
const teamGoalsController = require('../controllers/teamGoalsController')
const teamActivitiesController = require('../controllers/teamActivitiesController')
const workspacesController = require('../controllers/workspacesController')

const router = express.Router()

// Validation middleware
const validateTeamCreation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Team name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('subscriptionTier')
    .optional()
    .isIn(['free', 'team', 'business', 'enterprise'])
    .withMessage('Invalid subscription tier')
]

const validateTeamUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Team name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters')
]

const validateMemberInvite = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('role')
    .optional()
    .isIn(['admin', 'manager', 'member', 'viewer'])
    .withMessage('Invalid role')
]

const validateGoalCreation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Goal title must be between 3 and 200 characters'),
  body('targetValue')
    .isFloat({ min: 0 })
    .withMessage('Target value must be a positive number'),
  body('startDate')
    .isISO8601()
    .withMessage('Valid start date is required'),
  body('endDate')
    .isISO8601()
    .withMessage('Valid end date is required')
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date')
      }
      return true
    })
]

const validateWorkspaceCreation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Workspace name must be between 2 and 100 characters'),
  body('type')
    .optional()
    .isIn(['project', 'department', 'client', 'campaign', 'general'])
    .withMessage('Invalid workspace type'),
  body('visibility')
    .optional()
    .isIn(['public', 'private', 'restricted'])
    .withMessage('Invalid visibility setting')
]

// Team routes
router.get('/', auth, teamsController.getUserTeams)
router.post('/', auth, validateTeamCreation, teamsController.createTeam)
router.get('/:teamId', auth, teamsController.getTeam)
router.put('/:teamId', auth, validateTeamUpdate, teamsController.updateTeam)
router.get('/:teamId/analytics', auth, teamsController.getTeamAnalytics)

// Team member routes
router.post('/:teamId/members/invite', auth, validateMemberInvite, teamsController.inviteMember)
router.post('/:teamId/members/accept', auth, teamsController.acceptInvitation)
router.put('/:teamId/members/:memberId/role', auth, teamsController.updateMemberRole)

// Team goals routes
router.get('/:teamId/goals', auth, teamGoalsController.getTeamGoals)
router.post('/:teamId/goals', auth, validateGoalCreation, teamGoalsController.createTeamGoal)
router.get('/:teamId/goals/:goalId', auth, teamGoalsController.getGoalDetails)
router.put('/:teamId/goals/:goalId/progress', auth, teamGoalsController.updateGoalProgress)
router.delete('/:teamId/goals/:goalId', auth, teamGoalsController.deleteGoal)

// Team activities routes
router.get('/:teamId/activities', auth, teamActivitiesController.getTeamActivities)
router.post('/:teamId/activities/mark-read', auth, teamActivitiesController.markActivitiesAsRead)
router.get('/:teamId/activities/stats', auth, teamActivitiesController.getActivityStats)
router.get('/:teamId/activities/realtime', auth, teamActivitiesController.getRealtimeUpdates)

// Workspace routes
router.get('/:teamId/workspaces', auth, workspacesController.getTeamWorkspaces)
router.post('/:teamId/workspaces', auth, validateWorkspaceCreation, workspacesController.createWorkspace)
router.get('/:teamId/workspaces/:workspaceId', auth, workspacesController.getWorkspace)
router.put('/:teamId/workspaces/:workspaceId', auth, workspacesController.updateWorkspace)
router.delete('/:teamId/workspaces/:workspaceId', auth, workspacesController.archiveWorkspace)
router.get('/:teamId/workspaces/:workspaceId/analytics', auth, workspacesController.getWorkspaceAnalytics)

module.exports = router
