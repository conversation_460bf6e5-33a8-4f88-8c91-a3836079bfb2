/**
 * Team Collaboration Routes
 * Handles team management and collaboration features
 */

const express = require('express');
const teamController = require('../controllers/teamController');
const { authenticate, restrictTo } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// Team management
router.get('/', teamController.getTeams);
router.post('/', teamController.createTeam);
router.get('/:id', teamController.getTeam);
router.patch('/:id', teamController.updateTeam);
router.delete('/:id', teamController.deleteTeam);

// Team membership
router.post('/:id/join', teamController.joinTeam);
router.post('/:id/leave', teamController.leaveTeam);
router.post('/:id/invite', teamController.inviteToTeam);
router.post('/:id/remove-member', teamController.removeMember);

// Team roles and permissions
router.get('/:id/members', teamController.getTeamMembers);
router.patch('/:id/members/:userId/role', teamController.updateMemberRole);

// Team goals and objectives
router.get('/:id/goals', teamController.getTeamGoals);
router.post('/:id/goals', teamController.createTeamGoal);
router.patch('/:id/goals/:goalId', teamController.updateTeamGoal);
router.delete('/:id/goals/:goalId', teamController.deleteTeamGoal);

// Team progress tracking
router.get('/:id/progress', teamController.getTeamProgress);
router.get('/:id/analytics', teamController.getTeamAnalytics);
router.get('/:id/leaderboard', teamController.getTeamLeaderboard);

// Team communication
router.get('/:id/activity', teamController.getTeamActivity);
router.post('/:id/announcements', teamController.createAnnouncement);
router.get('/:id/announcements', teamController.getAnnouncements);

module.exports = router;
