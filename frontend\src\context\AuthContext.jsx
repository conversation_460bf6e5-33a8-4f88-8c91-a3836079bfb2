import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import { authService } from '@services/authService'
import { tokenService } from '@services/tokenService'

// Initial state
const initialState = {
  user: null,
  loading: true,
  isAuthenticated: false,
  error: null
}

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT: 'LOGOUT',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_USER: 'UPDATE_USER'
}

// Reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      }
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        loading: false,
        error: null
      }
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        loading: false,
        error: null
      }
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      }
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      }
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      }
    default:
      return state
  }
}

// Create context
const AuthContext = createContext()

// Auth provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState)
  const queryClient = useQueryClient()

  // Check if user is authenticated on app load
  const { data: user, isLoading } = useQuery(
    'currentUser',
    authService.getCurrentUser,
    {
      enabled: !!tokenService.getToken(),
      retry: false,
      onSuccess: (userData) => {
        dispatch({ type: AUTH_ACTIONS.LOGIN_SUCCESS, payload: userData })
      },
      onError: () => {
        tokenService.removeToken()
        dispatch({ type: AUTH_ACTIONS.LOGOUT })
      },
      onSettled: () => {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false })
      }
    }
  )

  // Login mutation
  const loginMutation = useMutation(authService.login, {
    onSuccess: (data) => {
      tokenService.setToken(data.token)
      dispatch({ type: AUTH_ACTIONS.LOGIN_SUCCESS, payload: data.user })
      queryClient.setQueryData('currentUser', data.user)
      toast.success('Welcome back!')
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Login failed'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: message })
      toast.error(message)
    }
  })

  // Register mutation
  const registerMutation = useMutation(authService.register, {
    onSuccess: (data) => {
      tokenService.setToken(data.token)
      dispatch({ type: AUTH_ACTIONS.LOGIN_SUCCESS, payload: data.user })
      queryClient.setQueryData('currentUser', data.user)
      toast.success('Account created successfully!')
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Registration failed'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: message })
      toast.error(message)
    }
  })

  // Logout mutation
  const logoutMutation = useMutation(authService.logout, {
    onSuccess: () => {
      tokenService.removeToken()
      dispatch({ type: AUTH_ACTIONS.LOGOUT })
      queryClient.clear()
      toast.success('Logged out successfully')
    },
    onError: () => {
      // Even if logout fails on server, clear local state
      tokenService.removeToken()
      dispatch({ type: AUTH_ACTIONS.LOGOUT })
      queryClient.clear()
    }
  })

  // Auth functions
  const login = async (credentials) => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
    return loginMutation.mutateAsync(credentials)
  }

  const register = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
    return registerMutation.mutateAsync(userData)
  }

  const logout = async () => {
    return logoutMutation.mutateAsync()
  }

  const updateUser = (userData) => {
    dispatch({ type: AUTH_ACTIONS.UPDATE_USER, payload: userData })
    queryClient.setQueryData('currentUser', (old) => ({ ...old, ...userData }))
  }

  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
  }

  // Set loading state based on query loading
  useEffect(() => {
    if (!tokenService.getToken()) {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false })
    }
  }, [])

  const value = {
    // State
    user: state.user,
    loading: state.loading || isLoading,
    isAuthenticated: state.isAuthenticated,
    error: state.error,
    
    // Actions
    login,
    register,
    logout,
    updateUser,
    clearError,
    
    // Mutation states
    isLoggingIn: loginMutation.isLoading,
    isRegistering: registerMutation.isLoading,
    isLoggingOut: logoutMutation.isLoading
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
