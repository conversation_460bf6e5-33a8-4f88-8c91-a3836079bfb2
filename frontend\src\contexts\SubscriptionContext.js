import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { subscriptionAPI } from '../services/api'

const SubscriptionContext = createContext()

// Action types
const SUBSCRIPTION_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_SUBSCRIPTION: 'SET_SUBSCRIPTION',
  SET_USAGE: 'SET_USAGE',
  SET_PLANS: 'SET_PLANS',
  SET_ERROR: 'SET_ERROR',
  SET_UPGRADE_PROMPTS: 'SET_UPGRADE_PROMPTS',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_USAGE: 'UPDATE_USAGE'
}

// Initial state
const initialState = {
  subscription: null,
  usage: null,
  plans: null,
  upgradePrompts: [],
  loading: false,
  error: null
}

// Reducer
function subscriptionReducer(state, action) {
  switch (action.type) {
    case SUBSCRIPTION_ACTIONS.SET_LOADING:
      return { ...state, loading: action.payload }
    
    case SUBSCRIPTION_ACTIONS.SET_SUBSCRIPTION:
      return { ...state, subscription: action.payload, loading: false }
    
    case SUBSCRIPTION_ACTIONS.SET_USAGE:
      return { ...state, usage: action.payload }
    
    case SUBSCRIPTION_ACTIONS.SET_PLANS:
      return { ...state, plans: action.payload }
    
    case SUBSCRIPTION_ACTIONS.SET_ERROR:
      return { ...state, error: action.payload, loading: false }
    
    case SUBSCRIPTION_ACTIONS.SET_UPGRADE_PROMPTS:
      return { ...state, upgradePrompts: action.payload }
    
    case SUBSCRIPTION_ACTIONS.CLEAR_ERROR:
      return { ...state, error: null }
    
    case SUBSCRIPTION_ACTIONS.UPDATE_USAGE:
      return {
        ...state,
        usage: state.usage ? {
          ...state.usage,
          features: {
            ...state.usage.features,
            [action.payload.feature]: {
              ...state.usage.features[action.payload.feature],
              ...action.payload.updates
            }
          }
        } : null
      }
    
    default:
      return state
  }
}

// Provider component
export function SubscriptionProvider({ children }) {
  const [state, dispatch] = useReducer(subscriptionReducer, initialState)

  // Load subscription data
  const loadSubscription = async () => {
    try {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_LOADING, payload: true })
      const response = await subscriptionAPI.getCurrentSubscription()
      
      if (response.success) {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_SUBSCRIPTION, payload: response.data.subscription })
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_USAGE, payload: response.data.usage })
      } else {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: response.message })
      }
    } catch (error) {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: error.message })
    }
  }

  // Load available plans
  const loadPlans = async (targetFeature = null) => {
    try {
      const response = await subscriptionAPI.getPlans(targetFeature)
      
      if (response.success) {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_PLANS, payload: response.data })
      } else {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: response.message })
      }
    } catch (error) {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: error.message })
    }
  }

  // Create subscription
  const createSubscription = async (plan, billingCycle, paymentMethodId) => {
    try {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_LOADING, payload: true })
      const response = await subscriptionAPI.createSubscription({
        plan,
        billingCycle,
        paymentMethodId
      })
      
      if (response.success) {
        await loadSubscription() // Reload subscription data
        return response.data
      } else {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: response.message })
        return null
      }
    } catch (error) {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: error.message })
      return null
    }
  }

  // Update subscription
  const updateSubscription = async (plan, billingCycle) => {
    try {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_LOADING, payload: true })
      const response = await subscriptionAPI.updateSubscription({
        plan,
        billingCycle
      })
      
      if (response.success) {
        await loadSubscription() // Reload subscription data
        return true
      } else {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: response.message })
        return false
      }
    } catch (error) {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: error.message })
      return false
    }
  }

  // Cancel subscription
  const cancelSubscription = async (cancelAtPeriodEnd = true, reason = null) => {
    try {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_LOADING, payload: true })
      const response = await subscriptionAPI.cancelSubscription({
        cancelAtPeriodEnd,
        reason
      })
      
      if (response.success) {
        await loadSubscription() // Reload subscription data
        return true
      } else {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: response.message })
        return false
      }
    } catch (error) {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: error.message })
      return false
    }
  }

  // Reactivate subscription
  const reactivateSubscription = async () => {
    try {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_LOADING, payload: true })
      const response = await subscriptionAPI.reactivateSubscription()
      
      if (response.success) {
        await loadSubscription() // Reload subscription data
        return true
      } else {
        dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: response.message })
        return false
      }
    } catch (error) {
      dispatch({ type: SUBSCRIPTION_ACTIONS.SET_ERROR, payload: error.message })
      return false
    }
  }

  // Check feature access
  const checkFeatureAccess = async (feature) => {
    try {
      const response = await subscriptionAPI.checkFeatureAccess(feature)
      
      if (response.success) {
        return response.data
      } else {
        return { access: false, upgradePrompt: null }
      }
    } catch (error) {
      console.error('Error checking feature access:', error)
      return { access: false, upgradePrompt: null }
    }
  }

  // Update usage locally (for optimistic updates)
  const updateUsage = (feature, updates) => {
    dispatch({
      type: SUBSCRIPTION_ACTIONS.UPDATE_USAGE,
      payload: { feature, updates }
    })
  }

  // Clear error
  const clearError = () => {
    dispatch({ type: SUBSCRIPTION_ACTIONS.CLEAR_ERROR })
  }

  // Helper functions
  const hasFeature = (feature) => {
    return state.subscription?.features?.[feature] === true || 
           state.subscription?.features?.[feature] === -1
  }

  const getFeatureLimit = (feature) => {
    return state.subscription?.features?.[feature] || 0
  }

  const getUsagePercentage = (feature) => {
    const usage = state.usage?.features?.[feature]
    if (!usage || usage.type !== 'limit') return 0
    
    if (usage.limit === -1) return 0 // unlimited
    return Math.min(100, (usage.usage / usage.limit) * 100)
  }

  const isAtLimit = (feature) => {
    const usage = state.usage?.features?.[feature]
    if (!usage || usage.type !== 'limit') return false
    
    if (usage.limit === -1) return false // unlimited
    return usage.usage >= usage.limit
  }

  const shouldShowUpgradePrompt = (feature) => {
    const percentage = getUsagePercentage(feature)
    return percentage >= 80 || !hasFeature(feature)
  }

  const isTrialing = () => {
    return state.subscription?.status === 'trialing'
  }

  const isPaid = () => {
    return state.subscription?.plan !== 'free'
  }

  const canUpgrade = () => {
    const plan = state.subscription?.plan
    return plan && ['free', 'team', 'business'].includes(plan)
  }

  // Load subscription on mount
  useEffect(() => {
    loadSubscription()
  }, [])

  const value = {
    // State
    ...state,
    
    // Actions
    loadSubscription,
    loadPlans,
    createSubscription,
    updateSubscription,
    cancelSubscription,
    reactivateSubscription,
    checkFeatureAccess,
    updateUsage,
    clearError,
    
    // Helper functions
    hasFeature,
    getFeatureLimit,
    getUsagePercentage,
    isAtLimit,
    shouldShowUpgradePrompt,
    isTrialing,
    isPaid,
    canUpgrade
  }

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  )
}

// Hook to use subscription context
export function useSubscription() {
  const context = useContext(SubscriptionContext)
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider')
  }
  return context
}

export default SubscriptionContext
