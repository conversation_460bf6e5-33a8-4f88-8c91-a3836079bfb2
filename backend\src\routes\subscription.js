const express = require('express')
const router = express.Router()
const { body } = require('express-validator')
const auth = require('../middleware/auth')
const subscriptionController = require('../controllers/subscriptionController')

// Validation middleware
const validateSubscriptionCreation = [
  body('plan')
    .isIn(['team', 'business', 'enterprise'])
    .withMessage('Invalid plan selected'),
  body('billingCycle')
    .isIn(['monthly', 'yearly'])
    .withMessage('Invalid billing cycle'),
  body('paymentMethodId')
    .optional()
    .isString()
    .withMessage('Invalid payment method ID')
]

const validateSubscriptionUpdate = [
  body('plan')
    .isIn(['team', 'business', 'enterprise'])
    .withMessage('Invalid plan selected'),
  body('billingCycle')
    .optional()
    .isIn(['monthly', 'yearly'])
    .withMessage('Invalid billing cycle')
]

const validateCancellation = [
  body('cancelAtPeriodEnd')
    .optional()
    .isBoolean()
    .withMessage('cancelAtPeriodEnd must be a boolean'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('Reason must be a string with max 500 characters')
]

// Public routes (no auth required)
router.post('/webhook', express.raw({ type: 'application/json' }), subscriptionController.handleWebhook)

// Protected routes (auth required)
router.use(auth)

// Get current subscription
router.get('/current', subscriptionController.getCurrentSubscription)

// Get available plans
router.get('/plans', subscriptionController.getPlans)

// Create new subscription
router.post('/', validateSubscriptionCreation, subscriptionController.createSubscription)

// Update subscription
router.put('/', validateSubscriptionUpdate, subscriptionController.updateSubscription)

// Cancel subscription
router.post('/cancel', validateCancellation, subscriptionController.cancelSubscription)

// Reactivate subscription
router.post('/reactivate', subscriptionController.reactivateSubscription)

// Get subscription history
router.get('/history', subscriptionController.getSubscriptionHistory)

// Check feature access
router.get('/features/:feature/access', subscriptionController.checkFeatureAccess)

module.exports = router
