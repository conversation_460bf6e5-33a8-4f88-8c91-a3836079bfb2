import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useQuery } from 'react-query'
import {
  ChartBarIcon,
  ClockIcon,
  TrendingUpIcon,
  CalendarIcon,
  FireIcon,
  EyeIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@services/api'
import Loading, { CardSkeleton } from '@components/Loading'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'

const Analytics = () => {
  const [timeRange, setTimeRange] = useState('30') // days
  const [selectedMetric, setSelectedMetric] = useState('time')

  // Fetch analytics data
  const { data: overviewData, isLoading: overviewLoading } = useQuery(
    ['analytics-overview', timeRange],
    () => apiHelpers.get(`/tracking/stats?days=${timeRange}`),
    {
      select: (response) => response.data.data.stats
    }
  )

  const { data: dailyData, isLoading: dailyLoading } = useQuery(
    ['analytics-daily', timeRange],
    () => apiHelpers.get(`/tracking/daily-stats?days=${timeRange}`),
    {
      select: (response) => response.data.data
    }
  )

  const { data: focusData, isLoading: focusLoading } = useQuery(
    ['analytics-focus', timeRange],
    () => apiHelpers.get(`/focus/insights?days=${timeRange}`),
    {
      select: (response) => response.data.data
    }
  )

  const { data: distractionData, isLoading: distractionLoading } = useQuery(
    ['analytics-distractions', timeRange],
    () => apiHelpers.get(`/focus/distraction-analysis?days=${timeRange}`),
    {
      select: (response) => response.data.data
    }
  )

  const isLoading = overviewLoading || dailyLoading || focusLoading || distractionLoading

  // Format time in hours and minutes
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }

  // Format percentage
  const formatPercentage = (value) => `${Math.round(value)}%`

  // Overview stats cards
  const overviewStats = [
    {
      title: 'Total Time Tracked',
      value: overviewData ? formatTime(overviewData.totalTime) : '0h 0m',
      change: '+12%',
      changeType: 'increase',
      icon: ClockIcon,
      color: 'text-blue-600'
    },
    {
      title: 'Focus Sessions',
      value: focusData?.totalSessions || 0,
      change: '+8%',
      changeType: 'increase',
      icon: FireIcon,
      color: 'text-orange-600'
    },
    {
      title: 'Productivity Score',
      value: overviewData ? `${Math.round(overviewData.averageProductivityScore)}%` : '0%',
      change: '+5%',
      changeType: 'increase',
      icon: TrendingUpIcon,
      color: 'text-green-600'
    },
    {
      title: 'Completion Rate',
      value: focusData ? formatPercentage(focusData.completionRate) : '0%',
      change: '+3%',
      changeType: 'increase',
      icon: ChartBarIcon,
      color: 'text-purple-600'
    }
  ]

  // Prepare chart data
  const productivityTrendData = dailyData?.hourlyData?.map((hour, index) => ({
    hour: `${index}:00`,
    productivity: hour.productivity,
    time: hour.totalTime / 60 // Convert to minutes
  })) || []

  const applicationUsageData = overviewData?.applicationUsage?.slice(0, 8).map(app => ({
    name: app.name,
    time: Math.round(app.time / 60), // Convert to minutes
    percentage: Math.round((app.time / overviewData.totalTime) * 100)
  })) || []

  const websiteUsageData = overviewData?.websiteUsage?.slice(0, 8).map(site => ({
    name: site.domain,
    time: Math.round(site.time / 60),
    percentage: Math.round((site.time / overviewData.totalTime) * 100)
  })) || []

  const productivityDistribution = overviewData?.productivityDistribution ? [
    { name: 'Very Productive', value: Math.round(overviewData.productivityDistribution.very_productive / 60), color: '#22c55e' },
    { name: 'Productive', value: Math.round(overviewData.productivityDistribution.productive / 60), color: '#84cc16' },
    { name: 'Neutral', value: Math.round(overviewData.productivityDistribution.neutral / 60), color: '#eab308' },
    { name: 'Distracting', value: Math.round(overviewData.productivityDistribution.distracting / 60), color: '#f97316' },
    { name: 'Very Distracting', value: Math.round(overviewData.productivityDistribution.very_distracting / 60), color: '#ef4444' }
  ].filter(item => item.value > 0) : []

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Analytics
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Comprehensive insights into your productivity patterns
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <CardSkeleton key={i} />
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <CardSkeleton key={i} className="h-80" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Analytics
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Comprehensive insights into your productivity patterns
          </p>
        </div>

        <div className="mt-4 sm:mt-0">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="form-select"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="365">Last year</option>
          </select>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="card p-6"
          >
            <div className="flex items-center">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700">
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                  <p className={`ml-2 text-sm font-medium ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Main Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Productivity Trend */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Productivity Trend
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Hourly productivity patterns
            </p>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={productivityTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'productivity' ? `${value}%` : `${value} min`,
                    name === 'productivity' ? 'Productivity' : 'Time Spent'
                  ]}
                />
                <Area
                  type="monotone"
                  dataKey="productivity"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Productivity Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Productivity Distribution
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Time spent by productivity level
            </p>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={productivityDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  dataKey="value"
                  label={({ name, percentage }) => `${name}: ${percentage}%`}
                >
                  {productivityDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} min`, 'Time']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Application Usage */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Top Applications
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Most used applications
            </p>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={applicationUsageData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip formatter={(value) => [`${value} min`, 'Time']} />
                <Bar dataKey="time" fill="#8b5cf6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Website Usage */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Top Websites
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Most visited websites
            </p>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={websiteUsageData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip formatter={(value) => [`${value} min`, 'Time']} />
                <Bar dataKey="time" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Focus & Distraction Analysis */}
      {focusData && distractionData && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Focus Session Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            className="card"
          >
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Focus Sessions
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Sessions</span>
                  <span className="font-semibold">{focusData.totalSessions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                  <span className="font-semibold">{focusData.completedSessions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completion Rate</span>
                  <span className="font-semibold">{formatPercentage(focusData.completionRate)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Avg Focus Score</span>
                  <span className="font-semibold">{Math.round(focusData.averageFocusScore)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Time</span>
                  <span className="font-semibold">{formatTime(focusData.totalPlannedTime * 60)}</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Distraction Analysis */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.9 }}
            className="card"
          >
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Distractions
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Distractions</span>
                  <span className="font-semibold">{distractionData.totalDistractions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Blocked</span>
                  <span className="font-semibold text-green-600">{distractionData.totalBlockedDistractions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Avg per Session</span>
                  <span className="font-semibold">{distractionData.averageDistractions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Blocking Effectiveness</span>
                  <span className="font-semibold">{formatPercentage(distractionData.blockingEffectiveness)}</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Best Performance Times */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1.0 }}
            className="card"
          >
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Peak Hours
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-3">
                {focusData.bestHours?.slice(0, 5).map((hour, index) => (
                  <div key={hour} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {hour}:00 - {hour + 1}:00
                    </span>
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${100 - (index * 20)}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500">#{index + 1}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Export and Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 1.1 }}
        className="card"
      >
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Export & Reports
          </h3>
        </div>
        <div className="card-body">
          <div className="flex flex-wrap gap-4">
            <button className="btn btn-primary">
              Export CSV
            </button>
            <button className="btn btn-secondary">
              Generate PDF Report
            </button>
            <button className="btn btn-secondary">
              Schedule Weekly Report
            </button>
            <button className="btn btn-outline">
              Share Dashboard
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default Analytics
