/**
 * Calendar Integration Routes
 * Handles calendar synchronization and event management
 */

const express = require('express');
const calendarController = require('../controllers/calendarController');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// Calendar connection management
router.get('/connections', calendarController.getConnections);
router.post('/connect/google', calendarController.connectGoogle);
router.post('/connect/outlook', calendarController.connectOutlook);
router.delete('/disconnect/:provider', calendarController.disconnect);

// Event management
router.get('/events', calendarController.getEvents);
router.post('/events', calendarController.createEvent);
router.get('/events/:id', calendarController.getEvent);
router.patch('/events/:id', calendarController.updateEvent);
router.delete('/events/:id', calendarController.deleteEvent);

// Calendar synchronization
router.post('/sync', calendarController.syncCalendars);
router.get('/sync/status', calendarController.getSyncStatus);
router.post('/sync/force', calendarController.forcSync);

// Schedule optimization
router.post('/optimize', calendarController.optimizeSchedule);
router.get('/conflicts', calendarController.getConflicts);
router.post('/resolve-conflicts', calendarController.resolveConflicts);

// Availability management
router.get('/availability', calendarController.getAvailability);
router.post('/availability', calendarController.setAvailability);
router.get('/free-slots', calendarController.getFreeSlots);

module.exports = router;
