# TimeWarp - AI-Powered Productivity Management Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen)](https://nodejs.org/)
[![React Version](https://img.shields.io/badge/react-%5E18.0.0-blue)](https://reactjs.org/)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/HectorTa1989/TimeWarp)

> Transform your productivity with AI-powered time management, intelligent scheduling, and behavioral pattern analysis.

## 🚀 Alternative Product Names & Branding

Here are 7 unique, brandable product name suggestions with available domain considerations:

1. **TimeWarp** (Current) - `timewarp.ai` / `timewarp.app`
2. **FlowSync** - `flowsync.io` / `flowsync.app`
3. **ProduxAI** - `produxai.com` / `produx.ai`
4. **ChronoBoost** - `chronoboost.io` / `chronoboost.app`
5. **FocusForge** - `focusforge.ai` / `focusforge.app`
6. **TaskMind** - `taskmind.ai` / `taskmind.app`
7. **ZenFlow** - `zenflow.ai` / `zenflow.app`

*Note: Domain availability should be verified before final selection*

## 📋 Table of Contents

- [Features Overview](#features-overview)
- [System Architecture](#system-architecture)
- [User Workflow](#user-workflow)
- [Project Structure](#project-structure)
- [Installation & Setup](#installation--setup)
- [API Documentation](#api-documentation)
- [Contributing](#contributing)
- [License](#license)

## ✨ Features Overview

### Core Productivity Features
- 🕒 **Automatic Time Tracking** - Cross-device and application monitoring
- 🤖 **AI-Powered Task Prioritization** - Machine learning-based task optimization
- 📅 **Intelligent Scheduling** - Smart calendar integration and conflict resolution
- 🎯 **Focus Mode** - Customizable website/app blocking with distraction analysis
- 📊 **Comprehensive Analytics** - Detailed productivity insights and pattern recognition
- 🍅 **Enhanced Pomodoro** - AI-suggested break timing based on energy levels
- 👥 **Team Collaboration** - Shared goals, progress tracking, and team insights
- 📈 **Habit Tracking** - Behavioral pattern analysis and improvement suggestions
- 🧠 **Personalized Coaching** - AI-driven productivity recommendations
- ⚡ **Energy Optimization** - Circadian rhythm-based scheduling

### Technical Features
- 🌐 **Cross-Platform** - Web, desktop (Electron), and mobile-ready
- 🔒 **End-to-End Encryption** - GDPR-compliant data protection
- 📱 **Responsive Design** - Optimized for all device sizes
- 🔄 **Real-time Sync** - WebSocket-powered live updates
- 📴 **Offline Support** - Core functionality without internet
- 🔌 **API Integrations** - Google Calendar, Outlook, Slack, and more

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web App - React]
        DESKTOP[Desktop App - Electron]
        MOBILE[Mobile App - React Native]
        EXT[Browser Extension]
    end
    
    subgraph "API Gateway"
        GATEWAY[Express.js API Gateway]
        AUTH[JWT Authentication]
        RATE[Rate Limiting]
    end
    
    subgraph "Core Services"
        TIME[Time Tracking Service]
        AI[AI/ML Service]
        SCHED[Scheduling Service]
        FOCUS[Focus Mode Service]
        ANALYTICS[Analytics Service]
        COLLAB[Collaboration Service]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
        FILES[File Storage]
    end
    
    subgraph "External APIs"
        GCAL[Google Calendar]
        OUTLOOK[Microsoft Graph]
        SLACK[Slack API]
        WEATHER[Weather API]
    end
    
    WEB --> GATEWAY
    DESKTOP --> GATEWAY
    MOBILE --> GATEWAY
    EXT --> GATEWAY
    
    GATEWAY --> AUTH
    GATEWAY --> RATE
    GATEWAY --> TIME
    GATEWAY --> AI
    GATEWAY --> SCHED
    GATEWAY --> FOCUS
    GATEWAY --> ANALYTICS
    GATEWAY --> COLLAB
    
    TIME --> POSTGRES
    AI --> POSTGRES
    SCHED --> POSTGRES
    FOCUS --> POSTGRES
    ANALYTICS --> POSTGRES
    COLLAB --> POSTGRES
    
    GATEWAY --> REDIS
    AI --> REDIS
    
    SCHED --> GCAL
    SCHED --> OUTLOOK
    COLLAB --> SLACK
    AI --> WEATHER
```

## 👤 User Workflow

```mermaid
flowchart TD
    START([User Opens TimeWarp]) --> LOGIN{Authenticated?}
    LOGIN -->|No| AUTH[Login/Register]
    LOGIN -->|Yes| DASHBOARD[Main Dashboard]
    AUTH --> ONBOARD[Onboarding Flow]
    
    ONBOARD --> SETUP_TRACKING[Setup Time Tracking]
    SETUP_TRACKING --> CONNECT_CALENDARS[Connect Calendars]
    CONNECT_CALENDARS --> SET_GOALS[Set Productivity Goals]
    SET_GOALS --> DASHBOARD
    
    DASHBOARD --> TRACK[Time Tracking Active]
    DASHBOARD --> SCHEDULE[View/Edit Schedule]
    DASHBOARD --> FOCUS_MODE[Activate Focus Mode]
    DASHBOARD --> ANALYTICS[View Analytics]
    DASHBOARD --> TEAM[Team Collaboration]
    
    TRACK --> AI_ANALYSIS[AI Pattern Analysis]
    AI_ANALYSIS --> SUGGESTIONS[Productivity Suggestions]
    SUGGESTIONS --> DASHBOARD
    
    SCHEDULE --> AI_OPTIMIZE[AI Schedule Optimization]
    AI_OPTIMIZE --> CALENDAR_SYNC[Sync with External Calendars]
    CALENDAR_SYNC --> DASHBOARD
    
    FOCUS_MODE --> BLOCK_DISTRACTIONS[Block Websites/Apps]
    BLOCK_DISTRACTIONS --> POMODORO[Enhanced Pomodoro Timer]
    POMODORO --> BREAK_SUGGESTIONS[AI Break Suggestions]
    BREAK_SUGGESTIONS --> DASHBOARD
    
    ANALYTICS --> INSIGHTS[Generate Insights]
    INSIGHTS --> COACHING[Personalized Coaching]
    COACHING --> DASHBOARD
    
    TEAM --> SHARED_GOALS[Shared Team Goals]
    SHARED_GOALS --> PROGRESS_TRACKING[Progress Tracking]
    PROGRESS_TRACKING --> DASHBOARD
```

## 📁 Project Structure

```
TimeWarp/
├── 📁 backend/                     # Node.js/Express API Server
│   ├── 📁 src/
│   │   ├── 📁 controllers/         # API route controllers
│   │   ├── 📁 middleware/          # Authentication, validation, etc.
│   │   ├── 📁 models/              # Database models (Sequelize)
│   │   ├── 📁 routes/              # API route definitions
│   │   ├── 📁 services/            # Business logic services
│   │   ├── 📁 utils/               # Utility functions
│   │   ├── 📁 ai/                  # Machine learning modules
│   │   └── 📄 app.js               # Express app configuration
│   ├── 📁 config/                  # Database and environment config
│   ├── 📁 migrations/              # Database migrations
│   ├── 📁 seeders/                 # Database seed data
│   ├── 📁 tests/                   # Backend tests
│   ├── 📄 package.json
│   └── 📄 server.js                # Server entry point
│
├── 📁 frontend/                    # React Web Application
│   ├── 📁 public/                  # Static assets
│   ├── 📁 src/
│   │   ├── 📁 components/          # Reusable React components
│   │   ├── 📁 pages/               # Page components
│   │   ├── 📁 hooks/               # Custom React hooks
│   │   ├── 📁 context/             # React context providers
│   │   ├── 📁 services/            # API service functions
│   │   ├── 📁 utils/               # Frontend utilities
│   │   ├── 📁 styles/              # CSS/SCSS styles
│   │   ├── 📁 assets/              # Images, icons, etc.
│   │   └── 📄 App.js               # Main App component
│   ├── 📄 package.json
│   └── 📄 vite.config.js           # Vite configuration
│
├── 📁 desktop/                     # Electron Desktop App
│   ├── 📁 src/
│   │   ├── 📄 main.js              # Electron main process
│   │   ├── 📄 preload.js           # Preload script
│   │   └── 📁 native/              # Native system integrations
│   ├── 📄 package.json
│   └── 📄 electron-builder.json    # Build configuration
│
├── 📁 browser-extension/           # Time Tracking Browser Extension
│   ├── 📁 src/
│   │   ├── 📄 manifest.json        # Extension manifest
│   │   ├── 📄 background.js        # Background script
│   │   ├── 📄 content.js           # Content script
│   │   └── 📄 popup.html           # Extension popup
│   └── 📄 webpack.config.js        # Build configuration
│
├── 📁 mobile/                      # React Native Mobile App (Future)
│   ├── 📁 src/
│   ├── 📄 package.json
│   └── 📄 metro.config.js
│
├── 📁 shared/                      # Shared utilities and types
│   ├── 📁 types/                   # TypeScript type definitions
│   ├── 📁 constants/               # Shared constants
│   └── 📁 utils/                   # Shared utility functions
│
├── 📁 docs/                        # Documentation
│   ├── 📄 API.md                   # API documentation
│   ├── 📄 DEPLOYMENT.md            # Deployment guide
│   └── 📄 CONTRIBUTING.md          # Contribution guidelines
│
├── 📁 scripts/                     # Build and deployment scripts
│   ├── 📄 setup.sh                 # Initial setup script
│   ├── 📄 deploy.sh                # Deployment script
│   └── 📄 test.sh                  # Test runner script
│
├── 📁 docker/                      # Docker configuration
│   ├── 📄 Dockerfile.backend       # Backend container
│   ├── 📄 Dockerfile.frontend      # Frontend container
│   └── 📄 docker-compose.yml       # Multi-container setup
│
├── 📄 .env.example                 # Environment variables template
├── 📄 .gitignore                   # Git ignore rules
├── 📄 .github/workflows/ci.yml     # GitHub Actions CI/CD
├── 📄 package.json                 # Root package.json
├── 📄 README.md                    # This file
└── 📄 LICENSE                      # MIT License
```

## 🚀 Installation & Setup

### Prerequisites

- **Node.js** (v16.0.0 or higher)
- **PostgreSQL** (v12 or higher)
- **Redis** (v6 or higher)
- **Git**
- **Docker** (optional, for containerized deployment)

### Quick Start

1. **Clone the Repository**
   ```bash
   git clone https://github.com/HectorTa1989/TimeWarp.git
   cd TimeWarp
   ```

2. **Install Dependencies**
   ```bash
   # Install root dependencies
   npm install

   # Install backend dependencies
   cd backend && npm install && cd ..

   # Install frontend dependencies
   cd frontend && npm install && cd ..

   # Install desktop app dependencies
   cd desktop && npm install && cd ..
   ```

3. **Environment Setup**
   ```bash
   # Copy environment template
   cp .env.example .env

   # Edit environment variables
   nano .env
   ```

4. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb timewarp_dev

   # Run migrations
   cd backend && npm run migrate && cd ..

   # Seed initial data
   cd backend && npm run seed && cd ..
   ```

5. **Start Development Servers**
   ```bash
   # Start all services concurrently
   npm run dev

   # Or start individually:
   # Backend API (Port 3001)
   cd backend && npm run dev

   # Frontend Web App (Port 3000)
   cd frontend && npm run dev

   # Desktop App
   cd desktop && npm run electron:dev
   ```

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/timewarp_dev
REDIS_URL=redis://localhost:6379

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# External API Keys
GOOGLE_CALENDAR_CLIENT_ID=your-google-client-id
GOOGLE_CALENDAR_CLIENT_SECRET=your-google-client-secret
MICROSOFT_GRAPH_CLIENT_ID=your-microsoft-client-id
MICROSOFT_GRAPH_CLIENT_SECRET=your-microsoft-client-secret

# AI/ML Configuration
OPENAI_API_KEY=your-openai-api-key (optional)
HUGGING_FACE_API_KEY=your-hugging-face-key (optional)

# Application Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### Docker Setup (Alternative)

```bash
# Build and start all services
docker-compose up --build

# Start in detached mode
docker-compose up -d

# Stop all services
docker-compose down
```

## 📚 API Documentation

### Authentication Endpoints

```http
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/profile
```

### Time Tracking Endpoints

```http
GET    /api/tracking/sessions
POST   /api/tracking/sessions
PUT    /api/tracking/sessions/:id
DELETE /api/tracking/sessions/:id
GET    /api/tracking/stats
```

### Task Management Endpoints

```http
GET    /api/tasks
POST   /api/tasks
PUT    /api/tasks/:id
DELETE /api/tasks/:id
POST   /api/tasks/:id/complete
GET    /api/tasks/ai-prioritize
```

### Calendar Integration Endpoints

```http
GET    /api/calendar/events
POST   /api/calendar/sync
GET    /api/calendar/conflicts
POST   /api/calendar/optimize
```

### Analytics Endpoints

```http
GET    /api/analytics/productivity
GET    /api/analytics/patterns
GET    /api/analytics/insights
GET    /api/analytics/reports
```

For detailed API documentation with request/response examples, see [docs/API.md](docs/API.md).

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests
cd backend && npm test

# Run frontend tests
cd frontend && npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## 🚀 Deployment

### Production Build

```bash
# Build all applications
npm run build

# Build specific applications
npm run build:frontend
npm run build:desktop
npm run build:extension
```

### Deployment Options

1. **Traditional Server Deployment**
   - Deploy backend to VPS/Cloud server
   - Serve frontend as static files via CDN
   - Use PM2 for process management

2. **Docker Deployment**
   - Use provided Docker configurations
   - Deploy to any container orchestration platform

3. **Cloud Platform Deployment**
   - **Vercel/Netlify**: Frontend deployment
   - **Heroku/Railway**: Backend deployment
   - **AWS/GCP/Azure**: Full-stack deployment

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](docs/CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass (`npm test`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

### Code Style

- Use ESLint and Prettier for code formatting
- Follow conventional commit messages
- Write comprehensive tests for new features
- Update documentation for API changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for AI/ML capabilities
- **Google Calendar API** for calendar integration
- **Microsoft Graph API** for Outlook integration
- **Chart.js** for data visualization
- **React** and **Node.js** communities

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [TimeWarp Community](https://discord.gg/timewarp)
- 🐛 Issues: [GitHub Issues](https://github.com/HectorTa1989/TimeWarp/issues)
- 📖 Documentation: [docs.timewarp.ai](https://docs.timewarp.ai)

---

**Built with ❤️ by [HectorTa1989](https://github.com/HectorTa1989)**

*Transform your productivity. Master your time. Warp into success.*
