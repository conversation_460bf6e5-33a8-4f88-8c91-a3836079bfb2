/**
 * Task Management Routes
 * Handles task creation, management, and AI-powered prioritization
 */

const express = require('express');
const taskController = require('../controllers/taskController');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// Task CRUD operations
router.get('/', taskController.getTasks);
router.post('/', taskController.createTask);
router.get('/:id', taskController.getTask);
router.patch('/:id', taskController.updateTask);
router.delete('/:id', taskController.deleteTask);

// Task status management
router.patch('/:id/complete', taskController.completeTask);
router.patch('/:id/uncomplete', taskController.uncompleteTask);
router.patch('/:id/archive', taskController.archiveTask);
router.patch('/:id/unarchive', taskController.unarchiveTask);

// AI-powered features
router.post('/ai-prioritize', taskController.aiPrioritizeTasks);
router.post('/ai-schedule', taskController.aiScheduleTasks);
router.get('/ai-suggestions', taskController.getAISuggestions);

// Task categories and tags
router.get('/categories', taskController.getCategories);
router.post('/categories', taskController.createCategory);
router.get('/tags', taskController.getTags);
router.post('/tags', taskController.createTag);

// Task templates
router.get('/templates', taskController.getTemplates);
router.post('/templates', taskController.createTemplate);
router.post('/templates/:id/use', taskController.useTemplate);

// Bulk operations
router.patch('/bulk/complete', taskController.bulkComplete);
router.patch('/bulk/delete', taskController.bulkDelete);
router.patch('/bulk/update', taskController.bulkUpdate);

module.exports = router;
