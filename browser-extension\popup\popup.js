/**
 * TimeWarp Browser Extension - Popup JavaScript
 * Handles popup UI interactions and communication with background script
 */

// DOM elements
const statusIndicator = document.getElementById('statusIndicator');
const currentSession = document.getElementById('currentSession');
const focusMode = document.getElementById('focusMode');
const focusSettings = document.getElementById('focusSettings');
const focusActive = document.getElementById('focusActive');
const focusToggle = document.getElementById('focusToggle');
const trackingToggle = document.getElementById('trackingToggle');
const loadingOverlay = document.getElementById('loadingOverlay');

// State
let currentState = {
  isTracking: false,
  currentSession: null,
  focusSession: null,
  todayStats: null
};

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  await loadCurrentState();
  setupEventListeners();
  updateUI();
  
  // Update every second for timers
  setInterval(updateTimers, 1000);
  
  // Refresh data every 30 seconds
  setInterval(loadCurrentState, 30000);
});

async function loadCurrentState() {
  try {
    showLoading(true);
    
    // Get status from background script
    const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
    
    currentState.isTracking = response.isTracking;
    currentState.currentSession = response.currentSession;
    currentState.focusSession = response.focusSession;
    currentState.lastActiveTab = response.lastActiveTab;
    
    // Load today's stats
    await loadTodayStats();
    
  } catch (error) {
    console.error('Error loading state:', error);
    showError('Failed to load data');
  } finally {
    showLoading(false);
  }
}

async function loadTodayStats() {
  try {
    const settings = await chrome.storage.sync.get(['apiUrl', 'authToken']);
    
    if (!settings.authToken) {
      // Show login prompt
      showLoginPrompt();
      return;
    }
    
    const today = new Date().toISOString().split('T')[0];
    
    // Fetch today's tracking stats
    const trackingResponse = await fetch(`${settings.apiUrl}/tracking/daily-stats?date=${today}`, {
      headers: {
        'Authorization': `Bearer ${settings.authToken}`
      }
    });
    
    // Fetch today's focus stats
    const focusResponse = await fetch(`${settings.apiUrl}/focus/insights?days=1`, {
      headers: {
        'Authorization': `Bearer ${settings.authToken}`
      }
    });
    
    if (trackingResponse.ok && focusResponse.ok) {
      const trackingData = await trackingResponse.json();
      const focusData = await focusResponse.json();
      
      currentState.todayStats = {
        totalTime: trackingData.data.totalTime || 0,
        focusSessions: focusData.data.totalSessions || 0,
        productivityScore: Math.round(focusData.data.averageFocusScore || 0)
      };
    }
  } catch (error) {
    console.error('Error loading today stats:', error);
  }
}

function setupEventListeners() {
  // Focus mode toggle
  focusToggle.addEventListener('click', toggleFocusMode);
  
  // Focus controls
  document.getElementById('pauseFocus')?.addEventListener('click', pauseFocusMode);
  document.getElementById('stopFocus')?.addEventListener('click', stopFocusMode);
  
  // Tracking toggle
  trackingToggle.addEventListener('click', toggleTracking);
  
  // Quick actions
  document.getElementById('quickTimer').addEventListener('click', startQuickTimer);
  document.getElementById('viewDashboard').addEventListener('click', openDashboard);
  
  // Footer links
  document.getElementById('settingsBtn').addEventListener('click', openSettings);
  document.getElementById('helpBtn').addEventListener('click', openHelp);
  document.getElementById('upgradeBtn').addEventListener('click', openUpgrade);
}

function updateUI() {
  updateStatusIndicator();
  updateCurrentSession();
  updateFocusMode();
  updateTodayStats();
  updateTrackingButton();
}

function updateStatusIndicator() {
  const statusDot = statusIndicator.querySelector('.status-dot');
  const statusText = statusIndicator.querySelector('.status-text');
  
  if (currentState.focusSession) {
    statusDot.className = 'status-dot focus';
    statusText.textContent = 'Focus Mode';
  } else if (currentState.isTracking) {
    statusDot.className = 'status-dot active';
    statusText.textContent = 'Tracking';
  } else {
    statusDot.className = 'status-dot';
    statusText.textContent = 'Inactive';
  }
}

function updateCurrentSession() {
  if (currentState.currentSession && currentState.lastActiveTab) {
    currentSession.style.display = 'block';
    
    const siteName = document.getElementById('siteName');
    const sessionTime = document.getElementById('sessionTime');
    
    try {
      const domain = new URL(currentState.lastActiveTab.url).hostname;
      siteName.textContent = domain;
    } catch {
      siteName.textContent = 'Unknown Site';
    }
    
    // Update session time (will be updated by updateTimers)
  } else {
    currentSession.style.display = 'none';
  }
}

function updateFocusMode() {
  if (currentState.focusSession) {
    focusSettings.style.display = 'none';
    focusActive.style.display = 'block';
    focusToggle.textContent = 'End Focus';
    focusToggle.className = 'btn btn-danger';
    
    // Update focus session UI (timer will be updated by updateTimers)
    updateFocusStats();
  } else {
    focusSettings.style.display = 'block';
    focusActive.style.display = 'none';
    focusToggle.textContent = 'Start Focus';
    focusToggle.className = 'btn btn-primary';
  }
}

function updateFocusStats() {
  if (!currentState.focusSession) return;
  
  const session = currentState.focusSession.data.session;
  
  // Update distraction count
  const distractionCount = document.getElementById('distractionCount');
  if (distractionCount) {
    distractionCount.textContent = session.distractionCount || 0;
  }
  
  // Update focus score
  const focusScore = document.getElementById('focusScore');
  if (focusScore) {
    focusScore.textContent = session.focusScore || 100;
  }
}

function updateTodayStats() {
  if (!currentState.todayStats) return;
  
  const todayTime = document.getElementById('todayTime');
  const focusSessions = document.getElementById('focusSessions');
  const productivityScore = document.getElementById('productivityScore');
  
  if (todayTime) {
    const hours = Math.floor(currentState.todayStats.totalTime / 3600);
    const minutes = Math.floor((currentState.todayStats.totalTime % 3600) / 60);
    todayTime.textContent = `${hours}h ${minutes}m`;
  }
  
  if (focusSessions) {
    focusSessions.textContent = currentState.todayStats.focusSessions;
  }
  
  if (productivityScore) {
    productivityScore.textContent = currentState.todayStats.productivityScore || '--';
  }
}

function updateTrackingButton() {
  const btnText = trackingToggle.querySelector('.btn-text');
  
  if (currentState.isTracking) {
    btnText.textContent = 'Stop Tracking';
    trackingToggle.className = 'btn btn-danger';
  } else {
    btnText.textContent = 'Start Tracking';
    trackingToggle.className = 'btn btn-outline';
  }
}

function updateTimers() {
  updateSessionTimer();
  updateFocusTimer();
}

function updateSessionTimer() {
  if (!currentState.currentSession) return;
  
  const sessionTime = document.getElementById('sessionTime');
  if (!sessionTime) return;
  
  const startTime = new Date(currentState.currentSession.data.session.startTime);
  const elapsed = Math.floor((Date.now() - startTime.getTime()) / 1000);
  
  const minutes = Math.floor(elapsed / 60);
  const seconds = elapsed % 60;
  
  sessionTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function updateFocusTimer() {
  if (!currentState.focusSession) return;
  
  const focusTimer = document.getElementById('focusTimer');
  const timerProgress = document.getElementById('timerProgress');
  
  if (!focusTimer || !timerProgress) return;
  
  const session = currentState.focusSession.data.session;
  const startTime = new Date(session.startTime);
  const duration = session.duration * 60 * 1000; // Convert to milliseconds
  const elapsed = Date.now() - startTime.getTime();
  const remaining = Math.max(0, duration - elapsed);
  
  if (remaining === 0) {
    focusTimer.textContent = '00:00';
    timerProgress.style.strokeDashoffset = '283';
    return;
  }
  
  const minutes = Math.floor(remaining / (60 * 1000));
  const seconds = Math.floor((remaining % (60 * 1000)) / 1000);
  
  focusTimer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  
  // Update progress circle
  const progress = (duration - remaining) / duration;
  const offset = 283 - (progress * 283);
  timerProgress.style.strokeDashoffset = offset.toString();
}

async function toggleFocusMode() {
  try {
    showLoading(true);
    
    if (currentState.focusSession) {
      await chrome.runtime.sendMessage({ action: 'stopFocusMode' });
    } else {
      const duration = parseInt(document.getElementById('focusDuration').value);
      const type = document.getElementById('focusType').value;
      const blockingEnabled = document.getElementById('blockingEnabled').checked;
      
      await chrome.runtime.sendMessage({
        action: 'startFocusMode',
        options: {
          duration,
          type,
          blockingEnabled
        }
      });
    }
    
    await loadCurrentState();
    updateUI();
    
  } catch (error) {
    console.error('Error toggling focus mode:', error);
    showError('Failed to toggle focus mode');
  } finally {
    showLoading(false);
  }
}

async function pauseFocusMode() {
  try {
    // Implementation for pause functionality
    showError('Pause functionality coming soon');
  } catch (error) {
    console.error('Error pausing focus mode:', error);
  }
}

async function stopFocusMode() {
  await toggleFocusMode();
}

async function toggleTracking() {
  try {
    showLoading(true);
    
    if (currentState.isTracking) {
      await chrome.runtime.sendMessage({ action: 'stopTracking' });
    } else {
      await chrome.runtime.sendMessage({ action: 'startTracking' });
    }
    
    await loadCurrentState();
    updateUI();
    
  } catch (error) {
    console.error('Error toggling tracking:', error);
    showError('Failed to toggle tracking');
  } finally {
    showLoading(false);
  }
}

async function startQuickTimer() {
  try {
    await chrome.runtime.sendMessage({
      action: 'startFocusMode',
      options: {
        duration: 15,
        type: 'custom',
        blockingEnabled: false
      }
    });
    
    await loadCurrentState();
    updateUI();
    
  } catch (error) {
    console.error('Error starting quick timer:', error);
    showError('Failed to start quick timer');
  }
}

function openDashboard() {
  chrome.tabs.create({ url: 'http://localhost:3000/dashboard' });
  window.close();
}

function openSettings() {
  chrome.runtime.openOptionsPage();
}

function openHelp() {
  chrome.tabs.create({ url: 'https://timewarp.ai/help' });
  window.close();
}

function openUpgrade() {
  chrome.tabs.create({ url: 'https://timewarp.ai/pricing' });
  window.close();
}

function showLoading(show) {
  loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showError(message) {
  // Simple error display - in production, use a proper notification system
  console.error(message);
  
  // You could implement a toast notification here
  const errorDiv = document.createElement('div');
  errorDiv.textContent = message;
  errorDiv.style.cssText = `
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    background: #ef4444;
    color: white;
    padding: 12px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 1000;
  `;
  
  document.body.appendChild(errorDiv);
  
  setTimeout(() => {
    errorDiv.remove();
  }, 3000);
}

function showLoginPrompt() {
  const loginDiv = document.createElement('div');
  loginDiv.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    ">
      <div style="
        background: white;
        padding: 24px;
        border-radius: 12px;
        text-align: center;
        max-width: 300px;
      ">
        <h3 style="margin: 0 0 16px 0;">Login Required</h3>
        <p style="margin: 0 0 20px 0; color: #666;">Please log in to TimeWarp to sync your data.</p>
        <button id="loginBtn" style="
          background: #3b82f6;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
        ">Open TimeWarp</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(loginDiv);
  
  document.getElementById('loginBtn').addEventListener('click', () => {
    chrome.tabs.create({ url: 'http://localhost:3000/auth/login' });
    window.close();
  });
}
