/**
 * Redis Configuration
 * Redis connection setup for caching and session management
 */

const redis = require('redis');
const logger = require('../utils/logger');

// Redis configuration
const redisConfig = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      logger.error('Redis server connection refused');
      return new Error('Redis server connection refused');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      logger.error('Redis retry time exhausted');
      return new Error('Retry time exhausted');
    }
    if (options.attempt > 10) {
      logger.error('Redis connection attempts exceeded');
      return undefined;
    }
    // Reconnect after
    return Math.min(options.attempt * 100, 3000);
  }
};

// Create Redis client
const client = redis.createClient(redisConfig);

// Redis event handlers
client.on('connect', () => {
  logger.info('Redis client connected');
});

client.on('ready', () => {
  logger.info('Redis client ready');
});

client.on('error', (err) => {
  logger.error('Redis client error:', err);
});

client.on('end', () => {
  logger.info('Redis client disconnected');
});

client.on('reconnecting', () => {
  logger.info('Redis client reconnecting');
});

/**
 * Connect to Redis
 */
async function connectRedis() {
  try {
    await client.connect();
    logger.info('Redis connection established successfully');
    return client;
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw error;
  }
}

/**
 * Disconnect from Redis
 */
async function disconnectRedis() {
  try {
    await client.quit();
    logger.info('Redis connection closed');
  } catch (error) {
    logger.error('Error closing Redis connection:', error);
    throw error;
  }
}

/**
 * Cache helper functions
 */
const cache = {
  /**
   * Set a value in cache with optional expiration
   */
  async set(key, value, expireInSeconds = 3600) {
    try {
      const serializedValue = JSON.stringify(value);
      if (expireInSeconds) {
        await client.setEx(key, expireInSeconds, serializedValue);
      } else {
        await client.set(key, serializedValue);
      }
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  },

  /**
   * Get a value from cache
   */
  async get(key) {
    try {
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  },

  /**
   * Delete a key from cache
   */
  async del(key) {
    try {
      await client.del(key);
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  },

  /**
   * Check if key exists in cache
   */
  async exists(key) {
    try {
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Cache exists error:', error);
      return false;
    }
  },

  /**
   * Set expiration for a key
   */
  async expire(key, seconds) {
    try {
      await client.expire(key, seconds);
      return true;
    } catch (error) {
      logger.error('Cache expire error:', error);
      return false;
    }
  },

  /**
   * Get all keys matching a pattern
   */
  async keys(pattern) {
    try {
      return await client.keys(pattern);
    } catch (error) {
      logger.error('Cache keys error:', error);
      return [];
    }
  },

  /**
   * Increment a numeric value
   */
  async incr(key) {
    try {
      return await client.incr(key);
    } catch (error) {
      logger.error('Cache increment error:', error);
      return null;
    }
  },

  /**
   * Decrement a numeric value
   */
  async decr(key) {
    try {
      return await client.decr(key);
    } catch (error) {
      logger.error('Cache decrement error:', error);
      return null;
    }
  }
};

module.exports = {
  client,
  connectRedis,
  disconnectRedis,
  cache
};
