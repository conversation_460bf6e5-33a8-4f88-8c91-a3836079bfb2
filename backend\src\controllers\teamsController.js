const { Team, TeamMember, TeamGoal, TeamActivity, Workspace, User } = require('../models')
const { Op } = require('sequelize')
const { validationResult } = require('express-validator')

// Get user's teams
exports.getUserTeams = async (req, res) => {
  try {
    const userId = req.user.id
    
    const teams = await Team.findAll({
      include: [
        {
          model: TeamMember,
          as: 'teamMembers',
          where: { userId, isActive: true },
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'avatar']
          }]
        },
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ],
      where: { isActive: true },
      order: [['createdAt', 'DESC']]
    })
    
    // Add user's role and permissions to each team
    const teamsWithRole = teams.map(team => {
      const member = team.teamMembers[0]
      return {
        ...team.toJSON(),
        userRole: member.role,
        userPermissions: member.permissions,
        memberStatus: member.status
      }
    })
    
    res.json({
      success: true,
      data: teamsWithRole
    })
  } catch (error) {
    console.error('Get user teams error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch teams',
      error: error.message
    })
  }
}

// Create new team
exports.createTeam = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }
    
    const { name, description, settings, subscriptionTier = 'free' } = req.body
    const userId = req.user.id
    
    // Check if user can create teams (implement limits based on subscription)
    const userTeamsCount = await TeamMember.count({
      where: { userId, isActive: true }
    })
    
    if (userTeamsCount >= 3 && subscriptionTier === 'free') {
      return res.status(403).json({
        success: false,
        message: 'Free users can only create up to 3 teams. Upgrade to create more teams.'
      })
    }
    
    const team = await Team.create({
      name,
      description,
      ownerId: userId,
      settings: settings || {},
      subscriptionTier,
      subscriptionStatus: subscriptionTier === 'free' ? 'active' : 'trial'
    })
    
    // Load team with associations
    const createdTeam = await Team.findByPk(team.id, {
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'name', 'email', 'avatar']
        },
        {
          model: TeamMember,
          as: 'teamMembers',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'avatar']
          }]
        }
      ]
    })
    
    res.status(201).json({
      success: true,
      data: createdTeam,
      message: 'Team created successfully'
    })
  } catch (error) {
    console.error('Create team error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create team',
      error: error.message
    })
  }
}

// Get team details
exports.getTeam = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    const team = await Team.findByPk(teamId, {
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'name', 'email', 'avatar']
        },
        {
          model: TeamMember,
          as: 'teamMembers',
          where: { isActive: true },
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'avatar', 'lastActiveAt']
          }],
          order: [['role', 'ASC'], ['joinedAt', 'ASC']]
        },
        {
          model: Workspace,
          as: 'workspaces',
          where: { status: 'active' },
          required: false,
          limit: 5
        }
      ]
    })
    
    if (!team) {
      return res.status(404).json({
        success: false,
        message: 'Team not found'
      })
    }
    
    // Add user's role and permissions
    const teamData = {
      ...team.toJSON(),
      userRole: member.role,
      userPermissions: member.permissions,
      memberStatus: member.status
    }
    
    res.json({
      success: true,
      data: teamData
    })
  } catch (error) {
    console.error('Get team error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch team',
      error: error.message
    })
  }
}

// Update team
exports.updateTeam = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    const { name, description, settings, avatar } = req.body
    
    // Check if user has permission to edit team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member || !member.hasPermission('canEditTeamSettings')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to edit team settings.'
      })
    }
    
    const team = await Team.findByPk(teamId)
    if (!team) {
      return res.status(404).json({
        success: false,
        message: 'Team not found'
      })
    }
    
    // Update team
    await team.update({
      name: name || team.name,
      description: description !== undefined ? description : team.description,
      settings: settings ? { ...team.settings, ...settings } : team.settings,
      avatar: avatar !== undefined ? avatar : team.avatar
    })
    
    // Log activity
    await TeamActivity.create({
      teamId,
      userId,
      type: 'team_updated',
      description: `updated team settings`,
      metadata: {
        changes: Object.keys(req.body)
      }
    })
    
    res.json({
      success: true,
      data: team,
      message: 'Team updated successfully'
    })
  } catch (error) {
    console.error('Update team error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update team',
      error: error.message
    })
  }
}

// Get team analytics
exports.getTeamAnalytics = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    const { period = '30d', startDate, endDate } = req.query
    
    // Check if user has permission to view analytics
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member || !member.hasPermission('canViewAnalytics')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to view team analytics.'
      })
    }
    
    // Calculate date range
    const end = endDate ? new Date(endDate) : new Date()
    let start
    
    switch (period) {
      case '7d':
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        start = new Date(end.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)
    }
    
    // Get team analytics data
    const team = await Team.findByPk(teamId)
    const members = await TeamMember.findAll({
      where: { teamId, isActive: true },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar']
      }]
    })
    
    // Get goals progress
    const goals = await TeamGoal.findAll({
      where: {
        teamId,
        startDate: { [Op.lte]: end },
        endDate: { [Op.gte]: start }
      }
    })
    
    // Get recent activities
    const activities = await TeamActivity.findAll({
      where: {
        teamId,
        createdAt: { [Op.between]: [start, end] }
      },
      order: [['createdAt', 'DESC']],
      limit: 100
    })
    
    // Calculate metrics
    const analytics = {
      overview: {
        totalMembers: members.length,
        activeMembers: members.filter(m => m.lastActiveAt && new Date(m.lastActiveAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length,
        totalGoals: goals.length,
        completedGoals: goals.filter(g => g.status === 'completed').length,
        totalActivities: activities.length
      },
      goals: {
        active: goals.filter(g => g.status === 'active'),
        completed: goals.filter(g => g.status === 'completed'),
        overdue: goals.filter(g => g.status === 'active' && new Date(g.endDate) < new Date())
      },
      members: members.map(member => ({
        ...member.toJSON(),
        recentActivities: activities.filter(a => a.userId === member.userId).length
      })),
      activityTrend: getActivityTrend(activities, start, end),
      productivityMetrics: team.stats
    }
    
    res.json({
      success: true,
      data: analytics
    })
  } catch (error) {
    console.error('Get team analytics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch team analytics',
      error: error.message
    })
  }
}

// Helper function to calculate activity trend
function getActivityTrend(activities, startDate, endDate) {
  const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
  const trend = []
  
  for (let i = 0; i < days; i++) {
    const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
    const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000)
    
    const dayActivities = activities.filter(a => {
      const activityDate = new Date(a.createdAt)
      return activityDate >= date && activityDate < nextDate
    })
    
    trend.push({
      date: date.toISOString().split('T')[0],
      count: dayActivities.length,
      types: dayActivities.reduce((acc, activity) => {
        acc[activity.type] = (acc[activity.type] || 0) + 1
        return acc
      }, {})
    })
  }
  
  return trend
}

// Invite member to team
exports.inviteMember = async (req, res) => {
  try {
    const { teamId } = req.params
    const { email, role = 'member' } = req.body
    const userId = req.user.id

    // Check if user has permission to invite members
    const inviter = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })

    if (!inviter || !inviter.hasPermission('canManageMembers')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to invite members.'
      })
    }

    const team = await Team.findByPk(teamId)
    if (!team) {
      return res.status(404).json({
        success: false,
        message: 'Team not found'
      })
    }

    // Check team member limit
    if (!team.canAddMember()) {
      return res.status(403).json({
        success: false,
        message: `Team has reached the maximum member limit of ${team.maxMembers}. Upgrade your subscription to add more members.`
      })
    }

    // Find user by email
    const invitedUser = await User.findOne({ where: { email } })
    if (!invitedUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found with this email address'
      })
    }

    // Check if user is already a member
    const existingMember = await TeamMember.findOne({
      where: { teamId, userId: invitedUser.id }
    })

    if (existingMember) {
      return res.status(400).json({
        success: false,
        message: 'User is already a member of this team'
      })
    }

    // Create team member invitation
    const member = await TeamMember.create({
      teamId,
      userId: invitedUser.id,
      role,
      status: 'pending',
      invitedById: userId
    })

    // Set role-based permissions
    member.setRolePermissions()
    await member.save()

    // Log activity
    await TeamActivity.createMemberActivity(teamId, userId, 'member_invited', member)

    // TODO: Send invitation email

    res.status(201).json({
      success: true,
      data: member,
      message: 'Member invited successfully'
    })
  } catch (error) {
    console.error('Invite member error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to invite member',
      error: error.message
    })
  }
}

// Accept team invitation
exports.acceptInvitation = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id

    const member = await TeamMember.findOne({
      where: { teamId, userId, status: 'pending' }
    })

    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Invitation not found or already processed'
      })
    }

    // Update member status
    member.status = 'active'
    member.joinedAt = new Date()
    await member.save()

    // Log activity
    await TeamActivity.createMemberActivity(teamId, userId, 'member_joined', member)

    // Update team stats
    const team = await Team.findByPk(teamId)
    await team.updateStats()

    res.json({
      success: true,
      data: member,
      message: 'Invitation accepted successfully'
    })
  } catch (error) {
    console.error('Accept invitation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to accept invitation',
      error: error.message
    })
  }
}

// Update member role
exports.updateMemberRole = async (req, res) => {
  try {
    const { teamId, memberId } = req.params
    const { role } = req.body
    const userId = req.user.id

    // Check if user has permission to manage members
    const manager = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })

    if (!manager || !manager.hasPermission('canManageMembers')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to manage members.'
      })
    }

    const member = await TeamMember.findOne({
      where: { id: memberId, teamId }
    })

    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Team member not found'
      })
    }

    // Prevent changing owner role
    const team = await Team.findByPk(teamId)
    if (team.ownerId === member.userId && role !== 'admin') {
      return res.status(400).json({
        success: false,
        message: 'Cannot change team owner role'
      })
    }

    const oldRole = member.role
    member.role = role
    member.setRolePermissions()
    await member.save()

    // Log activity
    await TeamActivity.createMemberActivity(teamId, userId, 'member_role_changed', member, {
      oldRole,
      newRole: role
    })

    res.json({
      success: true,
      data: member,
      message: 'Member role updated successfully'
    })
  } catch (error) {
    console.error('Update member role error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update member role',
      error: error.message
    })
  }
}

module.exports = {
  getUserTeams: exports.getUserTeams,
  createTeam: exports.createTeam,
  getTeam: exports.getTeam,
  updateTeam: exports.updateTeam,
  getTeamAnalytics: exports.getTeamAnalytics,
  inviteMember: exports.inviteMember,
  acceptInvitation: exports.acceptInvitation,
  updateMemberRole: exports.updateMemberRole
}
