<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TimeWarp - Productivity Tracker</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- Header -->
    <header class="popup-header">
      <div class="logo">
        <img src="../icons/icon-32.png" alt="TimeWarp" class="logo-icon">
        <span class="logo-text">TimeWarp</span>
      </div>
      <div class="status-indicator" id="statusIndicator">
        <span class="status-dot"></span>
        <span class="status-text">Inactive</span>
      </div>
    </header>

    <!-- Current Session -->
    <section class="current-session" id="currentSession" style="display: none;">
      <h3>Current Session</h3>
      <div class="session-info">
        <div class="session-site">
          <span class="site-favicon" id="siteFavicon">🌐</span>
          <span class="site-name" id="siteName">Loading...</span>
        </div>
        <div class="session-time">
          <span class="time-label">Time:</span>
          <span class="time-value" id="sessionTime">00:00</span>
        </div>
      </div>
    </section>

    <!-- Focus Mode -->
    <section class="focus-mode" id="focusMode">
      <div class="focus-header">
        <h3>Focus Mode</h3>
        <div class="focus-toggle">
          <button id="focusToggle" class="btn btn-primary">Start Focus</button>
        </div>
      </div>
      
      <div class="focus-settings" id="focusSettings">
        <div class="setting-group">
          <label for="focusDuration">Duration (minutes):</label>
          <select id="focusDuration">
            <option value="15">15 min</option>
            <option value="25" selected>25 min</option>
            <option value="45">45 min</option>
            <option value="60">60 min</option>
            <option value="90">90 min</option>
          </select>
        </div>
        
        <div class="setting-group">
          <label for="focusType">Session Type:</label>
          <select id="focusType">
            <option value="pomodoro">Pomodoro</option>
            <option value="deep_work">Deep Work</option>
            <option value="custom" selected>Custom</option>
          </select>
        </div>
        
        <div class="setting-group">
          <label>
            <input type="checkbox" id="blockingEnabled" checked>
            Enable website blocking
          </label>
        </div>
      </div>

      <div class="focus-active" id="focusActive" style="display: none;">
        <div class="focus-timer">
          <div class="timer-circle">
            <svg class="timer-svg" viewBox="0 0 100 100">
              <circle cx="50" cy="50" r="45" class="timer-bg"></circle>
              <circle cx="50" cy="50" r="45" class="timer-progress" id="timerProgress"></circle>
            </svg>
            <div class="timer-text">
              <span class="timer-time" id="focusTimer">25:00</span>
              <span class="timer-label">remaining</span>
            </div>
          </div>
        </div>
        
        <div class="focus-controls">
          <button id="pauseFocus" class="btn btn-secondary">Pause</button>
          <button id="stopFocus" class="btn btn-danger">Stop</button>
        </div>
        
        <div class="focus-stats">
          <div class="stat">
            <span class="stat-value" id="distractionCount">0</span>
            <span class="stat-label">Distractions</span>
          </div>
          <div class="stat">
            <span class="stat-value" id="focusScore">100</span>
            <span class="stat-label">Focus Score</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
      <h3>Quick Actions</h3>
      <div class="action-buttons">
        <button id="trackingToggle" class="btn btn-outline">
          <span class="btn-icon">⏱️</span>
          <span class="btn-text">Start Tracking</span>
        </button>
        
        <button id="quickTimer" class="btn btn-outline">
          <span class="btn-icon">⚡</span>
          <span class="btn-text">Quick Timer</span>
        </button>
        
        <button id="viewDashboard" class="btn btn-outline">
          <span class="btn-icon">📊</span>
          <span class="btn-text">Dashboard</span>
        </button>
      </div>
    </section>

    <!-- Today's Stats -->
    <section class="daily-stats">
      <h3>Today's Progress</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">⏰</div>
          <div class="stat-info">
            <span class="stat-value" id="todayTime">0h 0m</span>
            <span class="stat-label">Time Tracked</span>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🎯</div>
          <div class="stat-info">
            <span class="stat-value" id="focusSessions">0</span>
            <span class="stat-label">Focus Sessions</span>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📈</div>
          <div class="stat-info">
            <span class="stat-value" id="productivityScore">--</span>
            <span class="stat-label">Productivity</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="popup-footer">
      <div class="footer-links">
        <button id="settingsBtn" class="link-btn">Settings</button>
        <button id="helpBtn" class="link-btn">Help</button>
        <button id="upgradeBtn" class="link-btn premium">Upgrade</button>
      </div>
    </footer>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner"></div>
    <span class="loading-text">Loading...</span>
  </div>

  <script src="popup.js"></script>
</body>
</html>
