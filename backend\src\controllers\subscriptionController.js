const { Subscription, Payment, SubscriptionHistory, User } = require('../models')
const { billingService } = require('../services/billingService')
const { featureGateService } = require('../services/featureGateService')
const { validationResult } = require('express-validator')
const { Op } = require('sequelize')

class SubscriptionController {
  // Get current user's subscription details
  async getCurrentSubscription(req, res) {
    try {
      const userId = req.user.id
      
      const subscription = await featureGateService.getUserSubscription(userId)
      const usageSummary = await featureGateService.getUsageSummary(userId)
      
      // Get recent payments
      const recentPayments = await Payment.findAll({
        where: { userId },
        order: [['createdAt', 'DESC']],
        limit: 5,
        attributes: ['id', 'amount', 'currency', 'status', 'type', 'paidAt', 'createdAt']
      })

      res.json({
        success: true,
        data: {
          subscription: {
            id: subscription.id,
            plan: subscription.plan,
            status: subscription.status,
            billingCycle: subscription.billingCycle,
            amount: subscription.amount,
            currency: subscription.currency,
            currentPeriodStart: subscription.currentPeriodStart,
            currentPeriodEnd: subscription.currentPeriodEnd,
            trialEnd: subscription.trialEnd,
            cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
            nextBillingDate: subscription.nextBillingDate,
            isTrialing: subscription.isTrialing(),
            daysUntilExpiry: subscription.daysUntilExpiry()
          },
          usage: usageSummary,
          recentPayments
        }
      })
    } catch (error) {
      console.error('Error getting subscription:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to get subscription details'
      })
    }
  }

  // Get available plans and pricing
  async getPlans(req, res) {
    try {
      const userId = req.user.id
      const targetFeature = req.query.feature
      
      const upgradeOptions = await featureGateService.getUpgradeOptions(userId, targetFeature)
      
      const plans = {
        free: {
          name: 'Free',
          price: { monthly: 0, yearly: 0 },
          features: featureGateService.getPlanFeatures('free'),
          popular: false,
          current: false
        },
        team: {
          name: 'Team',
          price: { monthly: 12, yearly: 120 },
          features: featureGateService.getPlanFeatures('team'),
          popular: true,
          current: false,
          savings: 17
        },
        business: {
          name: 'Business',
          price: { monthly: 25, yearly: 250 },
          features: featureGateService.getPlanFeatures('business'),
          popular: false,
          current: false,
          savings: 17
        },
        enterprise: {
          name: 'Enterprise',
          price: { monthly: 50, yearly: 500 },
          features: featureGateService.getPlanFeatures('enterprise'),
          popular: false,
          current: false,
          savings: 17
        }
      }

      // Mark current plan
      const currentSubscription = await featureGateService.getUserSubscription(userId)
      if (plans[currentSubscription.plan]) {
        plans[currentSubscription.plan].current = true
      }

      res.json({
        success: true,
        data: {
          plans,
          upgradeOptions,
          currentPlan: currentSubscription.plan
        }
      })
    } catch (error) {
      console.error('Error getting plans:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to get plans'
      })
    }
  }

  // Create new subscription
  async createSubscription(req, res) {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        })
      }

      const userId = req.user.id
      const { plan, billingCycle, paymentMethodId } = req.body

      // Check if user already has an active subscription
      const existingSubscription = await Subscription.findOne({
        where: {
          userId,
          status: ['active', 'trialing']
        }
      })

      if (existingSubscription) {
        return res.status(400).json({
          success: false,
          message: 'User already has an active subscription'
        })
      }

      const result = await billingService.createSubscription(
        userId,
        plan,
        billingCycle,
        paymentMethodId
      )

      res.json({
        success: true,
        message: 'Subscription created successfully',
        data: {
          subscriptionId: result.subscription.id,
          clientSecret: result.clientSecret,
          status: result.subscription.status
        }
      })
    } catch (error) {
      console.error('Error creating subscription:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to create subscription'
      })
    }
  }

  // Update existing subscription
  async updateSubscription(req, res) {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        })
      }

      const userId = req.user.id
      const { plan, billingCycle } = req.body

      const subscription = await Subscription.findOne({
        where: {
          userId,
          status: ['active', 'trialing']
        }
      })

      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'No active subscription found'
        })
      }

      const updatedSubscription = await billingService.updateSubscription(
        subscription.id,
        plan,
        billingCycle
      )

      res.json({
        success: true,
        message: 'Subscription updated successfully',
        data: {
          subscription: {
            id: updatedSubscription.id,
            plan: updatedSubscription.plan,
            billingCycle: updatedSubscription.billingCycle,
            amount: updatedSubscription.amount,
            status: updatedSubscription.status
          }
        }
      })
    } catch (error) {
      console.error('Error updating subscription:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to update subscription'
      })
    }
  }

  // Cancel subscription
  async cancelSubscription(req, res) {
    try {
      const userId = req.user.id
      const { cancelAtPeriodEnd = true, reason } = req.body

      const subscription = await Subscription.findOne({
        where: {
          userId,
          status: ['active', 'trialing']
        }
      })

      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'No active subscription found'
        })
      }

      const canceledSubscription = await billingService.cancelSubscription(
        subscription.id,
        cancelAtPeriodEnd
      )

      // Log cancellation reason
      if (reason) {
        await SubscriptionHistory.logAction({
          subscriptionId: subscription.id,
          userId,
          action: 'canceled',
          metadata: { reason },
          source: 'user'
        })
      }

      res.json({
        success: true,
        message: cancelAtPeriodEnd 
          ? 'Subscription will be canceled at the end of the current period'
          : 'Subscription canceled immediately',
        data: {
          subscription: {
            id: canceledSubscription.id,
            status: canceledSubscription.status,
            cancelAtPeriodEnd: canceledSubscription.cancelAtPeriodEnd,
            currentPeriodEnd: canceledSubscription.currentPeriodEnd
          }
        }
      })
    } catch (error) {
      console.error('Error canceling subscription:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to cancel subscription'
      })
    }
  }

  // Reactivate subscription
  async reactivateSubscription(req, res) {
    try {
      const userId = req.user.id

      const subscription = await Subscription.findOne({
        where: {
          userId,
          cancelAtPeriodEnd: true
        }
      })

      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'No subscription found to reactivate'
        })
      }

      const reactivatedSubscription = await billingService.reactivateSubscription(subscription.id)

      res.json({
        success: true,
        message: 'Subscription reactivated successfully',
        data: {
          subscription: {
            id: reactivatedSubscription.id,
            status: reactivatedSubscription.status,
            cancelAtPeriodEnd: reactivatedSubscription.cancelAtPeriodEnd
          }
        }
      })
    } catch (error) {
      console.error('Error reactivating subscription:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to reactivate subscription'
      })
    }
  }

  // Get subscription history
  async getSubscriptionHistory(req, res) {
    try {
      const userId = req.user.id
      const page = parseInt(req.query.page) || 1
      const limit = parseInt(req.query.limit) || 20
      const offset = (page - 1) * limit

      const { count, rows: history } = await SubscriptionHistory.findAndCountAll({
        where: { userId },
        order: [['createdAt', 'DESC']],
        limit,
        offset,
        attributes: [
          'id', 'action', 'oldValue', 'newValue', 'amount', 
          'currency', 'metadata', 'source', 'createdAt'
        ]
      })

      res.json({
        success: true,
        data: {
          history,
          pagination: {
            page,
            limit,
            total: count,
            pages: Math.ceil(count / limit)
          }
        }
      })
    } catch (error) {
      console.error('Error getting subscription history:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to get subscription history'
      })
    }
  }

  // Check feature access
  async checkFeatureAccess(req, res) {
    try {
      const userId = req.user.id
      const { feature } = req.params

      const access = await featureGateService.checkFeatureAccess(userId, feature)
      const upgradePrompt = await featureGateService.shouldShowUpgradePrompt(userId, feature)

      res.json({
        success: true,
        data: {
          feature,
          access,
          upgradePrompt
        }
      })
    } catch (error) {
      console.error('Error checking feature access:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to check feature access'
      })
    }
  }

  // Handle Stripe webhooks
  async handleWebhook(req, res) {
    try {
      const signature = req.headers['stripe-signature']
      const body = req.body

      await billingService.handleWebhook(body, signature)

      res.json({ received: true })
    } catch (error) {
      console.error('Webhook error:', error)
      res.status(400).json({
        success: false,
        message: 'Webhook processing failed'
      })
    }
  }
}

module.exports = new SubscriptionController()
