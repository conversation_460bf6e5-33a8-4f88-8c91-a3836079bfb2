#!/usr/bin/env node

/**
 * TimeWarp Backend Server
 * Main entry point for the Express.js API server
 */

const app = require('./src/app');
const { createServer } = require('http');
const { Server } = require('socket.io');
const logger = require('./src/utils/logger');
const { connectDatabase } = require('./src/config/database');
const { connectRedis } = require('./src/config/redis');

// Load environment variables
require('dotenv').config();

const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Create HTTP server
const server = createServer(app);

// Initialize Socket.IO for real-time features
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  // Join user-specific room for personalized updates
  socket.on('join-user-room', (userId) => {
    socket.join(`user-${userId}`);
    logger.info(`User ${userId} joined their room`);
  });

  // Handle time tracking events
  socket.on('start-tracking', (data) => {
    // Broadcast to user's room
    socket.to(`user-${data.userId}`).emit('tracking-started', data);
  });

  socket.on('stop-tracking', (data) => {
    socket.to(`user-${data.userId}`).emit('tracking-stopped', data);
  });

  // Handle focus mode events
  socket.on('focus-mode-start', (data) => {
    socket.to(`user-${data.userId}`).emit('focus-mode-activated', data);
  });

  socket.on('focus-mode-end', (data) => {
    socket.to(`user-${data.userId}`).emit('focus-mode-deactivated', data);
  });

  // Handle team collaboration events
  socket.on('join-team-room', (teamId) => {
    socket.join(`team-${teamId}`);
    logger.info(`Socket ${socket.id} joined team room: ${teamId}`);
  });

  socket.on('team-update', (data) => {
    socket.to(`team-${data.teamId}`).emit('team-progress-update', data);
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Make io available to the app
app.set('io', io);

/**
 * Initialize database connections and start server
 */
async function startServer() {
  try {
    // Connect to PostgreSQL
    await connectDatabase();
    logger.info('✅ PostgreSQL connected successfully');

    // Connect to Redis
    await connectRedis();
    logger.info('✅ Redis connected successfully');

    // Start the server
    server.listen(PORT, () => {
      logger.info(`🚀 TimeWarp Backend Server running on port ${PORT}`);
      logger.info(`📊 Environment: ${NODE_ENV}`);
      logger.info(`🌐 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
      
      if (NODE_ENV === 'development') {
        logger.info(`📖 API Documentation: http://localhost:${PORT}/api/docs`);
        logger.info(`🔍 Health Check: http://localhost:${PORT}/api/health`);
      }
    });

  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

/**
 * Graceful shutdown handling
 */
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();
