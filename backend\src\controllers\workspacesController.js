const { Workspace, Team, TeamMember, Project, User, TeamActivity } = require('../models')
const { Op } = require('sequelize')
const { validationResult } = require('express-validator')

// Get team workspaces
exports.getTeamWorkspaces = async (req, res) => {
  try {
    const { teamId } = req.params
    const userId = req.user.id
    const { status = 'active', type, page = 1, limit = 20 } = req.query
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    // Build where clause
    const where = { teamId, status }
    if (type) where.type = type
    
    const offset = (page - 1) * limit
    
    const { count, rows: workspaces } = await Workspace.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email', 'avatar']
        },
        {
          model: Project,
          as: 'projects',
          attributes: ['id', 'name', 'status'],
          limit: 5,
          required: false
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    })
    
    // Filter workspaces based on user access
    const accessibleWorkspaces = []
    for (const workspace of workspaces) {
      const canAccess = await workspace.canUserAccess(userId, member.role)
      if (canAccess) {
        accessibleWorkspaces.push({
          ...workspace.toJSON(),
          canEdit: workspace.canUserPerformAction('canEditSettings', member.role),
          canCreateProjects: workspace.canUserPerformAction('canCreateProjects', member.role),
          canInviteMembers: workspace.canUserPerformAction('canInviteMembers', member.role)
        })
      }
    }
    
    res.json({
      success: true,
      data: {
        workspaces: accessibleWorkspaces,
        pagination: {
          total: accessibleWorkspaces.length,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(accessibleWorkspaces.length / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get team workspaces error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workspaces',
      error: error.message
    })
  }
}

// Create workspace
exports.createWorkspace = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }
    
    const { teamId } = req.params
    const userId = req.user.id
    const {
      name,
      description,
      type,
      visibility,
      color,
      icon,
      tags,
      settings
    } = req.body
    
    // Check if user has permission to create workspaces
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member || !member.hasPermission('canCreateProjects')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to create workspaces.'
      })
    }
    
    const workspace = await Workspace.create({
      teamId,
      name,
      description,
      type,
      visibility: visibility || 'public',
      createdById: userId,
      color: color || '#3B82F6',
      icon: icon || 'folder',
      tags: tags || [],
      settings: settings || {}
    })
    
    // Load workspace with associations
    const createdWorkspace = await Workspace.findByPk(workspace.id, {
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    })
    
    res.status(201).json({
      success: true,
      data: createdWorkspace,
      message: 'Workspace created successfully'
    })
  } catch (error) {
    console.error('Create workspace error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create workspace',
      error: error.message
    })
  }
}

// Get workspace details
exports.getWorkspace = async (req, res) => {
  try {
    const { teamId, workspaceId } = req.params
    const userId = req.user.id
    
    // Check if user is member of the team
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    const workspace = await Workspace.findOne({
      where: { id: workspaceId, teamId },
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email', 'avatar']
        },
        {
          model: Project,
          as: 'projects',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'avatar']
          }],
          order: [['updatedAt', 'DESC']]
        }
      ]
    })
    
    if (!workspace) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      })
    }
    
    // Check if user can access this workspace
    const canAccess = await workspace.canUserAccess(userId, member.role)
    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to access this workspace.'
      })
    }
    
    // Get recent activity
    const recentActivity = await workspace.getActivityFeed(20, 0)
    
    res.json({
      success: true,
      data: {
        ...workspace.toJSON(),
        canEdit: workspace.canUserPerformAction('canEditSettings', member.role),
        canCreateProjects: workspace.canUserPerformAction('canCreateProjects', member.role),
        canInviteMembers: workspace.canUserPerformAction('canInviteMembers', member.role),
        canViewAnalytics: workspace.canUserPerformAction('canViewAnalytics', member.role),
        recentActivity: recentActivity.map(activity => ({
          ...activity.toJSON(),
          formattedTime: activity.getFormattedTime()
        }))
      }
    })
  } catch (error) {
    console.error('Get workspace error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workspace',
      error: error.message
    })
  }
}

// Update workspace
exports.updateWorkspace = async (req, res) => {
  try {
    const { teamId, workspaceId } = req.params
    const userId = req.user.id
    const {
      name,
      description,
      visibility,
      color,
      icon,
      tags,
      settings
    } = req.body
    
    // Check if user has permission to edit workspace
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    const workspace = await Workspace.findOne({
      where: { id: workspaceId, teamId }
    })
    
    if (!workspace) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      })
    }
    
    // Check permissions
    if (!workspace.canUserPerformAction('canEditSettings', member.role) && workspace.createdById !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to edit this workspace.'
      })
    }
    
    // Update workspace
    await workspace.update({
      name: name || workspace.name,
      description: description !== undefined ? description : workspace.description,
      visibility: visibility || workspace.visibility,
      color: color || workspace.color,
      icon: icon || workspace.icon,
      tags: tags || workspace.tags,
      settings: settings ? { ...workspace.settings, ...settings } : workspace.settings
    })
    
    res.json({
      success: true,
      data: workspace,
      message: 'Workspace updated successfully'
    })
  } catch (error) {
    console.error('Update workspace error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update workspace',
      error: error.message
    })
  }
}

// Archive workspace
exports.archiveWorkspace = async (req, res) => {
  try {
    const { teamId, workspaceId } = req.params
    const userId = req.user.id
    
    // Check if user has permission to archive workspace
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member || (!member.hasPermission('canDeleteProjects') && member.role !== 'admin')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to archive workspaces.'
      })
    }
    
    const workspace = await Workspace.findOne({
      where: { id: workspaceId, teamId }
    })
    
    if (!workspace) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      })
    }
    
    await workspace.update({
      status: 'archived',
      archivedAt: new Date()
    })
    
    // Log activity
    await TeamActivity.create({
      teamId,
      userId,
      type: 'workspace_archived',
      description: `archived workspace "${workspace.name}"`,
      entityType: 'workspace',
      entityId: workspace.id,
      metadata: {
        workspaceName: workspace.name
      }
    })
    
    res.json({
      success: true,
      message: 'Workspace archived successfully'
    })
  } catch (error) {
    console.error('Archive workspace error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to archive workspace',
      error: error.message
    })
  }
}

// Get workspace analytics
exports.getWorkspaceAnalytics = async (req, res) => {
  try {
    const { teamId, workspaceId } = req.params
    const userId = req.user.id
    const { period = '30d' } = req.query
    
    // Check permissions
    const member = await TeamMember.findOne({
      where: { teamId, userId, isActive: true }
    })
    
    if (!member) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a member of this team.'
      })
    }
    
    const workspace = await Workspace.findOne({
      where: { id: workspaceId, teamId }
    })
    
    if (!workspace) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      })
    }
    
    if (!workspace.canUserPerformAction('canViewAnalytics', member.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You do not have permission to view analytics.'
      })
    }
    
    // Calculate date range
    const end = new Date()
    let start
    
    switch (period) {
      case '7d':
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        start = new Date(end.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)
    }
    
    // Get productivity metrics
    const metrics = await workspace.getProductivityMetrics(start, end)
    
    // Update workspace stats
    await workspace.updateStats()
    
    res.json({
      success: true,
      data: {
        overview: workspace.stats,
        metrics,
        period: {
          start: start.toISOString(),
          end: end.toISOString(),
          days: Math.ceil((end - start) / (1000 * 60 * 60 * 24))
        }
      }
    })
  } catch (error) {
    console.error('Get workspace analytics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workspace analytics',
      error: error.message
    })
  }
}

module.exports = {
  getTeamWorkspaces: exports.getTeamWorkspaces,
  createWorkspace: exports.createWorkspace,
  getWorkspace: exports.getWorkspace,
  updateWorkspace: exports.updateWorkspace,
  archiveWorkspace: exports.archiveWorkspace,
  getWorkspaceAnalytics: exports.getWorkspaceAnalytics
}
