/**
 * User Routes
 * Handles user profile management and user-related operations
 */

const express = require('express');
const userController = require('../controllers/userController');
const { authenticate, restrictTo } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// User profile routes
router.get('/profile', userController.getProfile);
router.patch('/profile', userController.updateProfile);
router.delete('/profile', userController.deleteProfile);

// User settings routes
router.get('/settings', userController.getSettings);
router.patch('/settings', userController.updateSettings);

// User preferences routes
router.get('/preferences', userController.getPreferences);
router.patch('/preferences', userController.updatePreferences);

// Admin only routes
router.use(restrictTo('admin'));
router.get('/', userController.getAllUsers);
router.get('/:id', userController.getUser);
router.patch('/:id', userController.updateUser);
router.delete('/:id', userController.deleteUser);

module.exports = router;
