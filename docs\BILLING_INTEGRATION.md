# TimeWarp Freemium Model & Billing Integration

## Overview

This document outlines the comprehensive freemium model and billing integration system implemented for TimeWarp's commercial launch. The system provides feature gating, subscription management, usage tracking, and strategic upgrade prompts to drive revenue growth.

## Architecture

### Core Components

1. **Subscription Management System**
   - Multi-tier subscription plans (Free, Team, Business, Enterprise)
   - Flexible billing cycles (monthly/yearly)
   - Trial management with automatic conversion
   - Payment provider integration (Stripe primary)

2. **Feature Gating Service**
   - Real-time feature access control
   - Usage limit enforcement
   - Strategic upgrade prompt system
   - Comprehensive usage tracking

3. **Billing Service**
   - Stripe integration for payment processing
   - Webhook handling for subscription events
   - Invoice generation and payment tracking
   - Subscription lifecycle management

## Subscription Tiers

### Free Plan
- **Price**: $0/month
- **Features**:
  - 3 projects maximum
  - 1 team member (self)
  - Basic time tracking
  - 1GB storage
  - Basic reporting

### Team Plan (Most Popular)
- **Price**: $12/month, $120/year (17% savings)
- **Features**:
  - 25 projects
  - 10 team members
  - Team collaboration features
  - 10GB storage
  - Advanced analytics
  - Priority support

### Business Plan
- **Price**: $25/month, $250/year (17% savings)
- **Features**:
  - 100 projects
  - 50 team members
  - Multiple workspaces
  - 50GB storage
  - Custom integrations
  - API access
  - Advanced reporting

### Enterprise Plan
- **Price**: $50/month, $500/year (17% savings)
- **Features**:
  - Unlimited projects
  - Unlimited team members
  - Unlimited workspaces
  - 500GB storage
  - White-label options
  - Custom integrations
  - Dedicated support
  - SLA guarantees

## Implementation Details

### Backend Components

#### Models
- **Subscription**: Core subscription management with plan features and billing info
- **Payment**: Payment tracking with provider integration and invoice management
- **SubscriptionHistory**: Complete audit trail of all subscription changes

#### Services
- **FeatureGateService**: Handles feature access control and usage enforcement
- **BillingService**: Manages Stripe integration and subscription operations

#### Controllers & Routes
- **SubscriptionController**: API endpoints for subscription management
- **Feature Gate Middleware**: Automatic feature restriction enforcement

### Frontend Components

#### React Context
- **SubscriptionContext**: Global state management for subscription data

#### Components
- **PricingPlans**: Interactive pricing table with upgrade flows
- **UpgradePrompt**: Strategic upgrade prompts (modal, banner, inline)
- **UsageDashboard**: Real-time usage tracking and limit visualization

## Feature Gating System

### Access Control Types

1. **Boolean Features**: On/off access (e.g., Advanced Analytics)
2. **Limit-based Features**: Usage quotas (e.g., Projects, Team Members)

### Usage Enforcement

```javascript
// Automatic enforcement before resource creation
app.post('/api/projects', 
  auth, 
  features.projects.enforce(1), // Enforce project limit
  projectController.create
)

// Feature access checking
app.get('/api/analytics/advanced', 
  auth, 
  features.advancedAnalytics.require, // Require feature access
  analyticsController.getAdvanced
)
```

### Upgrade Prompts

Strategic placement throughout the application:
- **80% usage threshold**: Proactive upgrade suggestions
- **Limit reached**: Blocking prompts with clear upgrade path
- **Feature access**: Contextual prompts when accessing premium features

## Stripe Integration

### Setup Requirements

1. **Stripe Account**: Create account optimized for Vietnam market
2. **Product & Price Creation**: Set up subscription products in Stripe Dashboard
3. **Webhook Configuration**: Configure webhooks for subscription events
4. **Environment Variables**: Set up API keys and price IDs

### Key Features

- **Payment Methods**: Credit cards, digital wallets
- **Subscription Management**: Automatic billing, proration, cancellation
- **Invoice Generation**: Automatic invoicing with receipt delivery
- **Webhook Handling**: Real-time subscription status updates

## Database Schema

### Subscriptions Table
```sql
- id (UUID, Primary Key)
- userId (UUID, Foreign Key)
- plan (ENUM: free, team, business, enterprise)
- status (ENUM: active, canceled, past_due, trialing)
- billingCycle (ENUM: monthly, yearly)
- features (JSONB: feature configuration)
- usage (JSONB: current usage tracking)
- paymentProviderId (STRING: Stripe subscription ID)
- trialEnd (DATE: trial expiration)
- nextBillingDate (DATE: next billing)
```

### Payments Table
```sql
- id (UUID, Primary Key)
- userId (UUID, Foreign Key)
- subscriptionId (UUID, Foreign Key)
- amount (DECIMAL: payment amount)
- status (ENUM: pending, succeeded, failed, refunded)
- paymentProviderId (STRING: Stripe payment ID)
- paidAt (DATE: payment completion)
```

## API Endpoints

### Subscription Management
- `GET /api/subscription/current` - Get current subscription
- `GET /api/subscription/plans` - Get available plans
- `POST /api/subscription` - Create new subscription
- `PUT /api/subscription` - Update subscription
- `POST /api/subscription/cancel` - Cancel subscription
- `POST /api/subscription/reactivate` - Reactivate subscription

### Feature Access
- `GET /api/subscription/features/:feature/access` - Check feature access
- Middleware integration for automatic enforcement

### Webhooks
- `POST /api/subscription/webhook` - Stripe webhook handler

## Usage Tracking

### Real-time Monitoring
- Project creation/deletion
- Team member additions/removals
- Storage usage updates
- Feature access attempts

### Analytics Integration
- Conversion funnel tracking
- Usage pattern analysis
- Churn prediction
- Revenue optimization

## Security Considerations

### Data Protection
- Encrypted payment data storage
- PCI DSS compliance through Stripe
- GDPR-compliant data handling
- Secure webhook verification

### Access Control
- JWT-based authentication
- Role-based permissions
- Feature-level authorization
- Rate limiting on sensitive endpoints

## Deployment Configuration

### Environment Variables
```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Price IDs (from Stripe Dashboard)
STRIPE_TEAM_MONTHLY_PRICE_ID=price_...
STRIPE_TEAM_YEARLY_PRICE_ID=price_...
# ... additional price IDs
```

### Database Migration
```bash
npm run migrate
```

### Stripe Setup
1. Create products and prices in Stripe Dashboard
2. Configure webhook endpoints
3. Update environment variables with price IDs
4. Test webhook delivery

## Testing Strategy

### Unit Tests
- Feature gating logic
- Subscription calculations
- Usage tracking accuracy
- Payment processing flows

### Integration Tests
- Stripe webhook handling
- Database consistency
- API endpoint functionality
- Frontend component behavior

### End-to-End Tests
- Complete subscription flows
- Payment processing
- Feature access enforcement
- Upgrade conversion paths

## Monitoring & Analytics

### Key Metrics
- **Conversion Rate**: Free to paid conversion
- **Churn Rate**: Subscription cancellations
- **ARPU**: Average revenue per user
- **LTV**: Customer lifetime value
- **Usage Patterns**: Feature adoption rates

### Alerting
- Payment failures
- High churn periods
- Usage limit breaches
- System errors

## Support & Maintenance

### Customer Support
- Subscription management tools
- Payment history access
- Usage analytics
- Billing dispute resolution

### Regular Maintenance
- Price optimization analysis
- Feature usage review
- Conversion funnel optimization
- Security updates

## Future Enhancements

### Planned Features
- Annual discount campaigns
- Referral program integration
- Usage-based pricing tiers
- Enterprise custom pricing
- Multi-currency support

### Optimization Opportunities
- A/B testing for pricing
- Dynamic upgrade prompts
- Personalized plan recommendations
- Advanced analytics integration

This comprehensive billing integration provides a solid foundation for TimeWarp's commercial success, with built-in scalability and optimization opportunities for continued growth.
