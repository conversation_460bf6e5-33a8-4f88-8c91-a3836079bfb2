const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY)
const { Subscription, Payment, SubscriptionHistory, User } = require('../models')

class BillingService {
  constructor() {
    this.stripe = stripe
    this.webhookSecret = process.env.STRIPE_WEBHOOK_SECRET
    
    // Plan configurations optimized for Vietnam market
    this.plans = {
      team: {
        monthly: {
          priceId: process.env.STRIPE_TEAM_MONTHLY_PRICE_ID,
          amount: 12, // USD - competitive for Vietnam market
          currency: 'usd'
        },
        yearly: {
          priceId: process.env.STRIPE_TEAM_YEARLY_PRICE_ID,
          amount: 120, // 17% discount
          currency: 'usd'
        }
      },
      business: {
        monthly: {
          priceId: process.env.STRIPE_BUSINESS_MONTHLY_PRICE_ID,
          amount: 25,
          currency: 'usd'
        },
        yearly: {
          priceId: process.env.STRIPE_BUSINESS_YEARLY_PRICE_ID,
          amount: 250,
          currency: 'usd'
        }
      },
      enterprise: {
        monthly: {
          priceId: process.env.STRIPE_ENTERPRISE_MONTHLY_PRICE_ID,
          amount: 50,
          currency: 'usd'
        },
        yearly: {
          priceId: process.env.STRIPE_ENTERPRISE_YEARLY_PRICE_ID,
          amount: 500,
          currency: 'usd'
        }
      }
    }
  }

  async createCustomer(user) {
    try {
      const customer = await this.stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user.id,
          source: 'timewarp'
        }
      })

      // Update user with Stripe customer ID
      await User.update(
        { 
          subscription: {
            ...user.subscription,
            customerId: customer.id,
            paymentProvider: 'stripe'
          }
        },
        { where: { id: user.id } }
      )

      return customer
    } catch (error) {
      console.error('Error creating Stripe customer:', error)
      throw new Error('Failed to create customer account')
    }
  }

  async createSubscription(userId, plan, billingCycle = 'monthly', paymentMethodId = null) {
    try {
      const user = await User.findByPk(userId)
      if (!user) throw new Error('User not found')

      // Get or create Stripe customer
      let customerId = user.subscription?.customerId
      if (!customerId) {
        const customer = await this.createCustomer(user)
        customerId = customer.id
      }

      // Get plan configuration
      const planConfig = this.plans[plan]?.[billingCycle]
      if (!planConfig) throw new Error('Invalid plan or billing cycle')

      // Create subscription with trial
      const subscriptionData = {
        customer: customerId,
        items: [{ price: planConfig.priceId }],
        trial_period_days: 14,
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          userId,
          plan,
          billingCycle,
          source: 'timewarp'
        }
      }

      // Add payment method if provided
      if (paymentMethodId) {
        subscriptionData.default_payment_method = paymentMethodId
      }

      const stripeSubscription = await this.stripe.subscriptions.create(subscriptionData)

      // Create local subscription record
      const subscription = await Subscription.create({
        userId,
        plan,
        status: 'trialing',
        billingCycle,
        amount: planConfig.amount,
        currency: planConfig.currency,
        paymentProviderId: stripeSubscription.id,
        paymentProvider: 'stripe',
        customerId,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        trialStart: new Date(stripeSubscription.trial_start * 1000),
        trialEnd: new Date(stripeSubscription.trial_end * 1000),
        nextBillingDate: new Date(stripeSubscription.current_period_end * 1000)
      })

      // Log subscription creation
      await SubscriptionHistory.logAction({
        subscriptionId: subscription.id,
        userId,
        action: 'created',
        newValue: plan,
        amount: planConfig.amount,
        currency: planConfig.currency,
        paymentProviderId: stripeSubscription.id,
        source: 'user'
      })

      return {
        subscription,
        stripeSubscription,
        clientSecret: stripeSubscription.latest_invoice?.payment_intent?.client_secret
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
      throw new Error('Failed to create subscription')
    }
  }

  async updateSubscription(subscriptionId, newPlan, newBillingCycle = null) {
    try {
      const subscription = await Subscription.findByPk(subscriptionId)
      if (!subscription) throw new Error('Subscription not found')

      const oldPlan = subscription.plan
      const billingCycle = newBillingCycle || subscription.billingCycle
      const planConfig = this.plans[newPlan]?.[billingCycle]
      
      if (!planConfig) throw new Error('Invalid plan configuration')

      // Update Stripe subscription
      const stripeSubscription = await this.stripe.subscriptions.update(
        subscription.paymentProviderId,
        {
          items: [{
            id: (await this.stripe.subscriptions.retrieve(subscription.paymentProviderId)).items.data[0].id,
            price: planConfig.priceId
          }],
          proration_behavior: 'create_prorations',
          metadata: {
            userId: subscription.userId,
            plan: newPlan,
            billingCycle,
            previousPlan: oldPlan
          }
        }
      )

      // Update local subscription
      await subscription.update({
        plan: newPlan,
        billingCycle,
        amount: planConfig.amount,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        nextBillingDate: new Date(stripeSubscription.current_period_end * 1000)
      })

      // Log the change
      const action = this.isUpgrade(oldPlan, newPlan) ? 'upgraded' : 'downgraded'
      await SubscriptionHistory.logAction({
        subscriptionId: subscription.id,
        userId: subscription.userId,
        action,
        oldValue: oldPlan,
        newValue: newPlan,
        amount: planConfig.amount,
        currency: planConfig.currency,
        paymentProviderId: subscription.paymentProviderId,
        source: 'user'
      })

      return subscription
    } catch (error) {
      console.error('Error updating subscription:', error)
      throw new Error('Failed to update subscription')
    }
  }

  async cancelSubscription(subscriptionId, cancelAtPeriodEnd = true) {
    try {
      const subscription = await Subscription.findByPk(subscriptionId)
      if (!subscription) throw new Error('Subscription not found')

      // Cancel in Stripe
      if (cancelAtPeriodEnd) {
        await this.stripe.subscriptions.update(subscription.paymentProviderId, {
          cancel_at_period_end: true
        })
      } else {
        await this.stripe.subscriptions.cancel(subscription.paymentProviderId)
      }

      // Update local subscription
      await subscription.update({
        cancelAtPeriodEnd,
        canceledAt: cancelAtPeriodEnd ? null : new Date(),
        status: cancelAtPeriodEnd ? subscription.status : 'canceled'
      })

      // Log cancellation
      await SubscriptionHistory.logAction({
        subscriptionId: subscription.id,
        userId: subscription.userId,
        action: 'canceled',
        paymentProviderId: subscription.paymentProviderId,
        metadata: {
          cancelAtPeriodEnd,
          reason: 'user_requested'
        },
        source: 'user'
      })

      return subscription
    } catch (error) {
      console.error('Error canceling subscription:', error)
      throw new Error('Failed to cancel subscription')
    }
  }

  async reactivateSubscription(subscriptionId) {
    try {
      const subscription = await Subscription.findByPk(subscriptionId)
      if (!subscription) throw new Error('Subscription not found')

      // Reactivate in Stripe
      await this.stripe.subscriptions.update(subscription.paymentProviderId, {
        cancel_at_period_end: false
      })

      // Update local subscription
      await subscription.update({
        cancelAtPeriodEnd: false,
        canceledAt: null,
        status: 'active'
      })

      // Log reactivation
      await SubscriptionHistory.logAction({
        subscriptionId: subscription.id,
        userId: subscription.userId,
        action: 'reactivated',
        paymentProviderId: subscription.paymentProviderId,
        source: 'user'
      })

      return subscription
    } catch (error) {
      console.error('Error reactivating subscription:', error)
      throw new Error('Failed to reactivate subscription')
    }
  }

  async createPaymentIntent(amount, currency = 'usd', customerId = null, metadata = {}) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        customer: customerId,
        metadata,
        automatic_payment_methods: {
          enabled: true
        }
      })

      return paymentIntent
    } catch (error) {
      console.error('Error creating payment intent:', error)
      throw new Error('Failed to create payment intent')
    }
  }

  async handleWebhook(body, signature) {
    try {
      const event = this.stripe.webhooks.constructEvent(body, signature, this.webhookSecret)
      
      console.log(`Received webhook: ${event.type}`)

      switch (event.type) {
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object)
          break
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object)
          break
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object)
          break
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object)
          break
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object)
          break
        case 'customer.subscription.trial_will_end':
          await this.handleTrialWillEnd(event.data.object)
          break
        default:
          console.log(`Unhandled webhook event: ${event.type}`)
      }

      return { received: true }
    } catch (error) {
      console.error('Webhook error:', error)
      throw error
    }
  }

  isUpgrade(oldPlan, newPlan) {
    const hierarchy = ['free', 'team', 'business', 'enterprise']
    return hierarchy.indexOf(newPlan) > hierarchy.indexOf(oldPlan)
  }

  async handleSubscriptionCreated(stripeSubscription) {
    // Implementation for subscription created webhook
    console.log('Subscription created:', stripeSubscription.id)
  }

  async handleSubscriptionUpdated(stripeSubscription) {
    // Implementation for subscription updated webhook
    console.log('Subscription updated:', stripeSubscription.id)
  }

  async handleSubscriptionDeleted(stripeSubscription) {
    // Implementation for subscription deleted webhook
    console.log('Subscription deleted:', stripeSubscription.id)
  }

  async handlePaymentSucceeded(invoice) {
    // Implementation for payment succeeded webhook
    console.log('Payment succeeded:', invoice.id)
  }

  async handlePaymentFailed(invoice) {
    // Implementation for payment failed webhook
    console.log('Payment failed:', invoice.id)
  }

  async handleTrialWillEnd(stripeSubscription) {
    // Implementation for trial ending webhook
    console.log('Trial will end:', stripeSubscription.id)
  }
}

const billingService = new BillingService()

module.exports = {
  BillingService,
  billingService
}
