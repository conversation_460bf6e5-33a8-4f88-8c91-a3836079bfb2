import axios from 'axios'
import { tokenService } from './tokenService'
import toast from 'react-hot-toast'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = tokenService.getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    // Calculate request duration for debugging
    const duration = new Date() - response.config.metadata.startTime
    console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`)
    
    return response
  },
  (error) => {
    const { response, request, config } = error
    
    // Calculate request duration for debugging
    if (config?.metadata?.startTime) {
      const duration = new Date() - config.metadata.startTime
      console.error(`API Error: ${config.method?.toUpperCase()} ${config.url} - ${duration}ms`)
    }
    
    // Handle different error scenarios
    if (response) {
      // Server responded with error status
      const { status, data } = response
      
      switch (status) {
        case 401:
          // Unauthorized - token expired or invalid
          tokenService.removeToken()
          if (!config.url?.includes('/auth/')) {
            toast.error('Session expired. Please log in again.')
            // Redirect to login page
            window.location.href = '/auth/login'
          }
          break
          
        case 403:
          // Forbidden
          toast.error('You do not have permission to perform this action.')
          break
          
        case 404:
          // Not found
          if (!config._retry) {
            toast.error('Resource not found.')
          }
          break
          
        case 422:
          // Validation error
          if (data?.errors && Array.isArray(data.errors)) {
            data.errors.forEach(err => {
              toast.error(err.message || 'Validation error')
            })
          } else {
            toast.error(data?.message || 'Validation failed.')
          }
          break
          
        case 429:
          // Rate limit exceeded
          toast.error('Too many requests. Please try again later.')
          break
          
        case 500:
          // Internal server error
          toast.error('Server error. Please try again later.')
          break
          
        default:
          // Other errors
          toast.error(data?.message || 'An unexpected error occurred.')
      }
      
      return Promise.reject(error)
    } else if (request) {
      // Network error
      if (error.code === 'ECONNABORTED') {
        toast.error('Request timeout. Please check your connection.')
      } else {
        toast.error('Network error. Please check your connection.')
      }
      return Promise.reject(error)
    } else {
      // Something else happened
      toast.error('An unexpected error occurred.')
      return Promise.reject(error)
    }
  }
)

// Retry mechanism for failed requests
const retryRequest = async (originalRequest, retries = 3, delay = 1000) => {
  for (let i = 0; i < retries; i++) {
    try {
      return await api(originalRequest)
    } catch (error) {
      if (i === retries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
}

// API helper functions
export const apiHelpers = {
  // GET request
  get: (url, config = {}) => api.get(url, config),
  
  // POST request
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  
  // PUT request
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  
  // PATCH request
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  
  // DELETE request
  delete: (url, config = {}) => api.delete(url, config),
  
  // Upload file
  upload: (url, formData, onUploadProgress) => {
    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    })
  },
  
  // Download file
  download: (url, filename) => {
    return api.get(url, {
      responseType: 'blob'
    }).then(response => {
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    })
  },
  
  // Retry failed request
  retry: retryRequest
}

// Subscription API
export const subscriptionAPI = {
  getCurrentSubscription: () => api.get('/subscription/current'),
  getPlans: (feature = null) => api.get(`/subscription/plans${feature ? `?feature=${feature}` : ''}`),
  createSubscription: (data) => api.post('/subscription', data),
  updateSubscription: (data) => api.put('/subscription', data),
  cancelSubscription: (data) => api.post('/subscription/cancel', data),
  reactivateSubscription: () => api.post('/subscription/reactivate'),
  getSubscriptionHistory: (page = 1, limit = 20) => api.get(`/subscription/history?page=${page}&limit=${limit}`),
  checkFeatureAccess: (feature) => api.get(`/subscription/features/${feature}/access`)
}

export default api
