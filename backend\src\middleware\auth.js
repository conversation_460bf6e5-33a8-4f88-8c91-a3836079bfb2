/**
 * Authentication Middleware
 * JWT-based authentication and authorization
 */

const jwt = require('jsonwebtoken');
const { promisify } = require('util');
const { AppError } = require('./errorHandler');
const { cache } = require('../config/redis');
const logger = require('../utils/logger');

/**
 * Verify JWT token and authenticate user
 */
const authenticate = async (req, res, next) => {
  try {
    // 1) Getting token and check if it exists
    let token;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    } else if (req.cookies && req.cookies.jwt) {
      token = req.cookies.jwt;
    }

    if (!token) {
      return next(new AppError('You are not logged in! Please log in to get access.', 401));
    }

    // 2) Check if token is blacklisted
    const isBlacklisted = await cache.get(`blacklist_${token}`);
    if (isBlacklisted) {
      return next(new AppError('Token has been invalidated. Please log in again.', 401));
    }

    // 3) Verification token
    const decoded = await promisify(jwt.verify)(token, process.env.JWT_SECRET);

    // 4) Check if user still exists (import User model when created)
    // const currentUser = await User.findByPk(decoded.id);
    // if (!currentUser) {
    //   return next(new AppError('The user belonging to this token does no longer exist.', 401));
    // }

    // 5) Check if user changed password after the token was issued
    // if (currentUser.changedPasswordAfter(decoded.iat)) {
    //   return next(new AppError('User recently changed password! Please log in again.', 401));
    // }

    // Grant access to protected route
    req.user = { id: decoded.id, email: decoded.email, role: decoded.role };
    req.token = token;
    
    // Log user activity
    logger.logUserAction('authenticated_request', decoded.id, {
      endpoint: req.originalUrl,
      method: req.method
    });
    
    next();
  } catch (error) {
    return next(new AppError('Invalid token. Please log in again.', 401));
  }
};

/**
 * Restrict access to specific roles
 */
const restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return next(new AppError('You do not have permission to perform this action', 403));
    }
    next();
  };
};

/**
 * Optional authentication - doesn't fail if no token
 */
const optionalAuth = async (req, res, next) => {
  try {
    let token;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      const decoded = await promisify(jwt.verify)(token, process.env.JWT_SECRET);
      req.user = { id: decoded.id, email: decoded.email, role: decoded.role };
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

/**
 * Check if user owns the resource or is admin
 */
const checkOwnership = (resourceUserIdField = 'userId') => {
  return (req, res, next) => {
    const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];
    
    if (req.user.role === 'admin' || req.user.id === parseInt(resourceUserId)) {
      return next();
    }
    
    return next(new AppError('You can only access your own resources', 403));
  };
};

/**
 * Rate limiting per user
 */
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  return async (req, res, next) => {
    if (!req.user) {
      return next();
    }
    
    const key = `rate_limit_${req.user.id}`;
    const current = await cache.get(key) || 0;
    
    if (current >= maxRequests) {
      return next(new AppError('Too many requests. Please try again later.', 429));
    }
    
    await cache.set(key, current + 1, Math.ceil(windowMs / 1000));
    next();
  };
};

/**
 * Validate API key for external integrations
 */
const validateApiKey = async (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey) {
      return next(new AppError('API key is required', 401));
    }
    
    // Check if API key exists in cache or database
    const keyData = await cache.get(`api_key_${apiKey}`);
    if (!keyData) {
      return next(new AppError('Invalid API key', 401));
    }
    
    req.apiKey = keyData;
    next();
  } catch (error) {
    return next(new AppError('Invalid API key', 401));
  }
};

module.exports = {
  authenticate,
  restrictTo,
  optionalAuth,
  checkOwnership,
  userRateLimit,
  validateApiKey
};
