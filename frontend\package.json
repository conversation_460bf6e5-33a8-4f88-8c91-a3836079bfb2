{"name": "timewarp-frontend", "version": "1.0.0", "description": "TimeWarp Frontend React Application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "lint": "eslint src --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext js,jsx,ts,tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "axios": "^1.6.2", "chart.js": "^4.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-calendar": "^4.6.0", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-datepicker": "^4.25.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.5.0", "react-time-picker": "^6.5.2", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwindcss": "^3.3.6", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.40.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "playwright": "^1.40.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.7", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}