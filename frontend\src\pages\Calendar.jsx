import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UserGroupIcon,
  LinkIcon,
  CogIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@services/api'
import { useSocket } from '@context/SocketContext'
import Loading from '@components/Loading'
import toast from 'react-hot-toast'

const Calendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState('month') // month, week, day
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [showEventModal, setShowEventModal] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [showIntegrationModal, setShowIntegrationModal] = useState(false)

  // New event form state
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    location: '',
    attendees: [],
    isTimeBlocked: false,
    projectId: null,
    taskId: null
  })

  const queryClient = useQueryClient()
  const { socket, isConnected } = useSocket()

  // Get calendar month/week/day range
  const getDateRange = () => {
    const start = new Date(currentDate)
    const end = new Date(currentDate)

    switch (viewMode) {
      case 'month':
        start.setDate(1)
        end.setMonth(end.getMonth() + 1, 0)
        break
      case 'week':
        const dayOfWeek = start.getDay()
        start.setDate(start.getDate() - dayOfWeek)
        end.setDate(start.getDate() + 6)
        break
      case 'day':
        end.setDate(start.getDate())
        break
    }

    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    }
  }

  // Fetch calendar events
  const { data: eventsData, isLoading: eventsLoading } = useQuery(
    ['calendar-events', currentDate, viewMode],
    () => {
      const { start, end } = getDateRange()
      return apiHelpers.get('/calendar/events', {
        params: { start, end, view: viewMode }
      })
    },
    {
      select: (response) => response.data.data.events || [],
      refetchInterval: 60000 // Refresh every minute
    }
  )

  // Fetch calendar integrations
  const { data: integrations } = useQuery(
    'calendar-integrations',
    () => apiHelpers.get('/calendar/integrations'),
    {
      select: (response) => response.data.data.integrations || []
    }
  )

  // Fetch projects and tasks for event linking
  const { data: projects } = useQuery(
    'projects',
    () => apiHelpers.get('/projects'),
    {
      select: (response) => response.data.data.projects || []
    }
  )

  const { data: tasks } = useQuery(
    'tasks',
    () => apiHelpers.get('/tasks?status=pending,in_progress'),
    {
      select: (response) => response.data.data.tasks || []
    }
  )

  // Create event mutation
  const createEventMutation = useMutation(
    (eventData) => apiHelpers.post('/calendar/events', eventData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('calendar-events')
        setShowEventModal(false)
        setNewEvent({
          title: '',
          description: '',
          startTime: '',
          endTime: '',
          location: '',
          attendees: [],
          isTimeBlocked: false,
          projectId: null,
          taskId: null
        })
        toast.success('Event created successfully!')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to create event')
      }
    }
  )

  // Sync calendar mutation
  const syncCalendarMutation = useMutation(
    (integrationId) => apiHelpers.post(`/calendar/integrations/${integrationId}/sync`),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('calendar-events')
        queryClient.invalidateQueries('calendar-integrations')
        toast.success('Calendar synced successfully!')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to sync calendar')
      }
    }
  )

  // Connect integration mutation
  const connectIntegrationMutation = useMutation(
    (provider) => apiHelpers.post('/calendar/integrations/connect', { provider }),
    {
      onSuccess: (response) => {
        if (response.data.data.authUrl) {
          window.open(response.data.data.authUrl, '_blank')
        }
        queryClient.invalidateQueries('calendar-integrations')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to connect calendar')
      }
    }
  )

  // Socket event listeners
  useEffect(() => {
    if (!socket || !isConnected) return

    const handleEventCreated = () => {
      queryClient.invalidateQueries('calendar-events')
    }

    const handleEventUpdated = () => {
      queryClient.invalidateQueries('calendar-events')
    }

    const handleCalendarSynced = () => {
      queryClient.invalidateQueries('calendar-events')
      queryClient.invalidateQueries('calendar-integrations')
    }

    socket.on('calendar-event-created', handleEventCreated)
    socket.on('calendar-event-updated', handleEventUpdated)
    socket.on('calendar-synced', handleCalendarSynced)

    return () => {
      socket.off('calendar-event-created', handleEventCreated)
      socket.off('calendar-event-updated', handleEventUpdated)
      socket.off('calendar-synced', handleCalendarSynced)
    }
  }, [socket, isConnected, queryClient])

  // Navigation functions
  const navigateDate = (direction) => {
    const newDate = new Date(currentDate)

    switch (viewMode) {
      case 'month':
        newDate.setMonth(newDate.getMonth() + direction)
        break
      case 'week':
        newDate.setDate(newDate.getDate() + (direction * 7))
        break
      case 'day':
        newDate.setDate(newDate.getDate() + direction)
        break
    }

    setCurrentDate(newDate)
  }

  const goToToday = () => {
    setCurrentDate(new Date())
  }

  // Format date for display
  const formatDateHeader = () => {
    const options = {
      year: 'numeric',
      month: 'long',
      ...(viewMode === 'day' && { day: 'numeric' })
    }
    return currentDate.toLocaleDateString('en-US', options)
  }

  // Generate calendar grid for month view
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const days = []
    const current = new Date(startDate)

    for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
      days.push(new Date(current))
      current.setDate(current.getDate() + 1)
    }

    return days
  }

  // Get events for a specific date
  const getEventsForDate = (date) => {
    if (!eventsData) return []

    const dateStr = date.toISOString().split('T')[0]
    return eventsData.filter(event => {
      const eventDate = new Date(event.startTime).toISOString().split('T')[0]
      return eventDate === dateStr
    })
  }

  // Handle event creation
  const handleCreateEvent = () => {
    if (!newEvent.title.trim()) {
      toast.error('Event title is required')
      return
    }

    if (!newEvent.startTime || !newEvent.endTime) {
      toast.error('Start and end times are required')
      return
    }

    createEventMutation.mutate({
      ...newEvent,
      attendees: newEvent.attendees.filter(email => email.trim()),
      projectId: newEvent.projectId || null,
      taskId: newEvent.taskId || null
    })
  }

  if (eventsLoading) {
    return <Loading fullScreen />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Calendar Integration
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Unified calendar view with time blocking and smart scheduling
          </p>
        </div>

        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button
            onClick={() => setShowIntegrationModal(true)}
            className="btn btn-secondary flex items-center"
          >
            <LinkIcon className="h-4 w-4 mr-2" />
            Integrations
          </button>

          <button
            onClick={() => setShowEventModal(true)}
            className="btn btn-primary flex items-center"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Event
          </button>
        </div>
      </div>

      {/* Calendar Controls */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            {/* Date Navigation */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => navigateDate(-1)}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>

                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 min-w-[200px] text-center">
                  {formatDateHeader()}
                </h2>

                <button
                  onClick={() => navigateDate(1)}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <ChevronRightIcon className="h-5 w-5" />
                </button>
              </div>

              <button
                onClick={goToToday}
                className="btn btn-secondary text-sm"
              >
                Today
              </button>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <div className="flex rounded-lg border border-gray-200 dark:border-gray-700">
                {['month', 'week', 'day'].map((mode) => (
                  <button
                    key={mode}
                    onClick={() => setViewMode(mode)}
                    className={`px-3 py-2 text-sm font-medium capitalize ${
                      viewMode === mode
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                    } ${mode === 'month' ? 'rounded-l-lg' : mode === 'day' ? 'rounded-r-lg' : ''}`}
                  >
                    {mode}
                  </button>
                ))}
              </div>

              {integrations && integrations.length > 0 && (
                <button
                  onClick={() => {
                    integrations.forEach(integration => {
                      if (integration.isConnected) {
                        syncCalendarMutation.mutate(integration.id)
                      }
                    })
                  }}
                  disabled={syncCalendarMutation.isLoading}
                  className="btn btn-secondary flex items-center"
                >
                  <ArrowPathIcon className={`h-4 w-4 mr-2 ${syncCalendarMutation.isLoading ? 'animate-spin' : ''}`} />
                  Sync
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Calendar View */}
      <div className="card">
        <div className="card-body p-0">
          {viewMode === 'month' && (
            <div className="p-4">
              {/* Month Header */}
              <div className="grid grid-cols-7 gap-1 mb-4">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                  <div key={day} className="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
                    {day}
                  </div>
                ))}
              </div>

              {/* Month Grid */}
              <div className="grid grid-cols-7 gap-1">
                {generateCalendarDays().map((day, index) => {
                  const isCurrentMonth = day.getMonth() === currentDate.getMonth()
                  const isToday = day.toDateString() === new Date().toDateString()
                  const dayEvents = getEventsForDate(day)

                  return (
                    <div
                      key={index}
                      className={`min-h-[120px] p-2 border border-gray-200 dark:border-gray-700 ${
                        isCurrentMonth ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-900'
                      } ${isToday ? 'ring-2 ring-blue-500' : ''} hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer`}
                      onClick={() => {
                        setSelectedDate(day)
                        setNewEvent(prev => ({
                          ...prev,
                          startTime: day.toISOString().split('T')[0] + 'T09:00',
                          endTime: day.toISOString().split('T')[0] + 'T10:00'
                        }))
                        setShowEventModal(true)
                      }}
                    >
                      <div className={`text-sm font-medium ${
                        isCurrentMonth ? 'text-gray-900 dark:text-gray-100' : 'text-gray-400'
                      } ${isToday ? 'text-blue-600' : ''}`}>
                        {day.getDate()}
                      </div>

                      <div className="mt-1 space-y-1">
                        {dayEvents.slice(0, 3).map((event, eventIndex) => (
                          <div
                            key={eventIndex}
                            className={`text-xs p-1 rounded truncate ${
                              event.isTimeBlocked
                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                            }`}
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedEvent(event)
                            }}
                          >
                            {event.title}
                          </div>
                        ))}
                        {dayEvents.length > 3 && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            +{dayEvents.length - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {viewMode === 'week' && (
            <div className="p-4">
              <div className="text-center text-gray-500 dark:text-gray-400 py-12">
                <CalendarIcon className="mx-auto h-12 w-12 mb-4" />
                <h3 className="text-lg font-medium mb-2">Week View</h3>
                <p>Week view implementation coming soon</p>
              </div>
            </div>
          )}

          {viewMode === 'day' && (
            <div className="p-4">
              <div className="text-center text-gray-500 dark:text-gray-400 py-12">
                <ClockIcon className="mx-auto h-12 w-12 mb-4" />
                <h3 className="text-lg font-medium mb-2">Day View</h3>
                <p>Day view implementation coming soon</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Integration Status */}
      {integrations && integrations.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Connected Calendars
            </h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {integrations.map((integration) => (
                <div
                  key={integration.id}
                  className={`p-4 rounded-lg border ${
                    integration.isConnected
                      ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                      : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        integration.isConnected ? 'bg-green-400' : 'bg-red-400'
                      }`} />
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {integration.provider}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {integration.isConnected ? 'Connected' : 'Disconnected'}
                        </p>
                      </div>
                    </div>

                    {integration.isConnected && (
                      <button
                        onClick={() => syncCalendarMutation.mutate(integration.id)}
                        disabled={syncCalendarMutation.isLoading}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        Sync
                      </button>
                    )}
                  </div>

                  {integration.lastSyncAt && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                      Last synced: {new Date(integration.lastSyncAt).toLocaleString()}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Add Event Modal */}
      <AnimatePresence>
        {showEventModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowEventModal(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
                  Create New Event
                </h3>

                <div className="space-y-4">
                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Event Title *
                    </label>
                    <input
                      type="text"
                      value={newEvent.title}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, title: e.target.value }))}
                      className="form-input w-full"
                      placeholder="Enter event title..."
                      autoFocus
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Description
                    </label>
                    <textarea
                      value={newEvent.description}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, description: e.target.value }))}
                      className="form-textarea w-full"
                      rows={3}
                      placeholder="Event description..."
                    />
                  </div>

                  {/* Time */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Start Time *
                      </label>
                      <input
                        type="datetime-local"
                        value={newEvent.startTime}
                        onChange={(e) => setNewEvent(prev => ({ ...prev, startTime: e.target.value }))}
                        className="form-input w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        End Time *
                      </label>
                      <input
                        type="datetime-local"
                        value={newEvent.endTime}
                        onChange={(e) => setNewEvent(prev => ({ ...prev, endTime: e.target.value }))}
                        className="form-input w-full"
                      />
                    </div>
                  </div>

                  {/* Location */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Location
                    </label>
                    <input
                      type="text"
                      value={newEvent.location}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, location: e.target.value }))}
                      className="form-input w-full"
                      placeholder="Meeting room, address, or online link..."
                    />
                  </div>

                  {/* Project and Task Linking */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Link to Project
                      </label>
                      <select
                        value={newEvent.projectId || ''}
                        onChange={(e) => setNewEvent(prev => ({ ...prev, projectId: e.target.value || null }))}
                        className="form-select w-full"
                      >
                        <option value="">No Project</option>
                        {projects?.map(project => (
                          <option key={project.id} value={project.id}>
                            {project.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Link to Task
                      </label>
                      <select
                        value={newEvent.taskId || ''}
                        onChange={(e) => setNewEvent(prev => ({ ...prev, taskId: e.target.value || null }))}
                        className="form-select w-full"
                      >
                        <option value="">No Task</option>
                        {tasks?.map(task => (
                          <option key={task.id} value={task.id}>
                            {task.title}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Time Blocking */}
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="timeBlocked"
                      checked={newEvent.isTimeBlocked}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, isTimeBlocked: e.target.checked }))}
                      className="form-checkbox"
                    />
                    <label htmlFor="timeBlocked" className="text-sm text-gray-700 dark:text-gray-300">
                      Block this time for focused work (prevents distractions)
                    </label>
                  </div>

                  {/* Attendees */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Attendees (comma-separated emails)
                    </label>
                    <input
                      type="text"
                      value={newEvent.attendees.join(', ')}
                      onChange={(e) => setNewEvent(prev => ({
                        ...prev,
                        attendees: e.target.value.split(',').map(email => email.trim()).filter(Boolean)
                      }))}
                      className="form-input w-full"
                      placeholder="<EMAIL>, <EMAIL>..."
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setShowEventModal(false)}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateEvent}
                    disabled={createEventMutation.isLoading || !newEvent.title.trim()}
                    className="btn btn-primary"
                  >
                    {createEventMutation.isLoading ? 'Creating...' : 'Create Event'}
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Integration Modal */}
      <AnimatePresence>
        {showIntegrationModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowIntegrationModal(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Calendar Integrations
                  </h3>
                  <button
                    onClick={() => setShowIntegrationModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Google Calendar */}
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <CalendarIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Google Calendar
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Sync events and create time blocks
                        </p>
                      </div>
                    </div>

                    <button
                      onClick={() => connectIntegrationMutation.mutate('google')}
                      disabled={connectIntegrationMutation.isLoading}
                      className="btn btn-primary text-sm"
                    >
                      {connectIntegrationMutation.isLoading ? 'Connecting...' : 'Connect'}
                    </button>
                  </div>

                  {/* Outlook Calendar */}
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <CalendarIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Microsoft Outlook
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Sync with Outlook calendar
                        </p>
                      </div>
                    </div>

                    <button
                      onClick={() => connectIntegrationMutation.mutate('outlook')}
                      disabled={connectIntegrationMutation.isLoading}
                      className="btn btn-primary text-sm"
                    >
                      {connectIntegrationMutation.isLoading ? 'Connecting...' : 'Connect'}
                    </button>
                  </div>

                  {/* Apple Calendar */}
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg opacity-50">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        <CalendarIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          Apple Calendar
                        </h4>
                        <p className="text-xs text-gray-400">
                          Coming soon
                        </p>
                      </div>
                    </div>

                    <button
                      disabled
                      className="btn btn-secondary text-sm opacity-50 cursor-not-allowed"
                    >
                      Coming Soon
                    </button>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        Privacy & Security
                      </h4>
                      <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                        We only access your calendar data to sync events and create time blocks.
                        Your data is encrypted and never shared with third parties.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export default Calendar
