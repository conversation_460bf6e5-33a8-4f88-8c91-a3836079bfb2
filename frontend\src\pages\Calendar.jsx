import React from 'react'
import { motion } from 'framer-motion'

const Calendar = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Calendar
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Calendar page content will be implemented here.
        </p>
      </div>

      <div className="card p-8 text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Coming Soon
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          This feature is currently under development and will be available soon.
        </p>
      </div>
    </motion.div>
  )
}

export default Calendar
