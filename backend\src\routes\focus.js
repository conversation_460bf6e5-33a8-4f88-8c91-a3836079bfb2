/**
 * Focus Mode Routes
 * Handles focus sessions, website blocking, and distraction management
 */

const express = require('express');
const focusController = require('../controllers/focusController');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// Focus session management
router.get('/sessions', focusController.getFocusSessions);
router.post('/sessions', focusController.createFocusSession);
router.get('/sessions/:id', focusController.getFocusSession);
router.patch('/sessions/:id', focusController.updateFocusSession);
router.delete('/sessions/:id', focusController.deleteFocusSession);

// Active focus session
router.get('/active', focusController.getActiveFocusSession);
router.post('/start', focusController.startFocusSession);
router.post('/stop', focusController.stopFocusSession);
router.post('/pause', focusController.pauseFocusSession);
router.post('/resume', focusController.resumeFocusSession);

// Website and app blocking
router.get('/blocklists', focusController.getBlocklists);
router.post('/blocklists', focusController.createBlocklist);
router.patch('/blocklists/:id', focusController.updateBlocklist);
router.delete('/blocklists/:id', focusController.deleteBlocklist);

// Pomodoro technique
router.get('/pomodoro/settings', focusController.getPomodoroSettings);
router.patch('/pomodoro/settings', focusController.updatePomodoroSettings);
router.post('/pomodoro/start', focusController.startPomodoro);
router.post('/pomodoro/break', focusController.startPomodoroBreak);

// Distraction tracking
router.get('/distractions', focusController.getDistractions);
router.post('/distractions', focusController.recordDistraction);
router.get('/distractions/analysis', focusController.getDistractionAnalysis);

// Focus insights
router.get('/insights', focusController.getFocusInsights);
router.get('/recommendations', focusController.getFocusRecommendations);

module.exports = router;
