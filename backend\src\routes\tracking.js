/**
 * Time Tracking Routes
 * Handles time tracking sessions and related operations
 */

const express = require('express');
const trackingController = require('../controllers/trackingController');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// Time tracking session routes
router.get('/sessions', trackingController.getSessions);
router.post('/sessions', trackingController.createSession);
router.get('/sessions/:id', trackingController.getSession);
router.patch('/sessions/:id', trackingController.updateSession);
router.delete('/sessions/:id', trackingController.deleteSession);

// Active session management
router.get('/active', trackingController.getActiveSession);
router.post('/start', trackingController.startTracking);
router.post('/stop', trackingController.stopTracking);
router.post('/pause', trackingController.pauseTracking);
router.post('/resume', trackingController.resumeTracking);

// Time tracking statistics
router.get('/stats', trackingController.getStats);
router.get('/stats/daily', trackingController.getDailyStats);
router.get('/stats/weekly', trackingController.getWeeklyStats);
router.get('/stats/monthly', trackingController.getMonthlyStats);

// Application tracking
router.get('/applications', trackingController.getTrackedApplications);
router.post('/applications/track', trackingController.trackApplication);

// Website tracking
router.get('/websites', trackingController.getTrackedWebsites);
router.post('/websites/track', trackingController.trackWebsite);

module.exports = router;
