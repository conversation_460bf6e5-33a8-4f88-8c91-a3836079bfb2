/**
 * Authentication Service
 * Handles all authentication-related API calls
 */

import { apiHelpers } from './api'

export const authService = {
  /**
   * Login user
   */
  async login(credentials) {
    const response = await apiHelpers.post('/auth/login', credentials)
    return response.data.data || response.data
  },

  /**
   * Register new user
   */
  async register(userData) {
    const response = await apiHelpers.post('/auth/register', userData)
    return response.data.data || response.data
  },

  /**
   * Logout user
   */
  async logout() {
    const response = await apiHelpers.post('/auth/logout')
    return response.data
  },

  /**
   * Get current user
   */
  async getCurrentUser() {
    const response = await apiHelpers.get('/auth/me')
    return response.data.data?.user || response.data.user
  },

  /**
   * Update current user profile
   */
  async updateProfile(userData) {
    const response = await apiHelpers.patch('/auth/update-me', userData)
    return response.data.data?.user || response.data.user
  },

  /**
   * Change password
   */
  async changePassword(passwordData) {
    const response = await apiHelpers.patch('/auth/change-password', passwordData)
    return response.data
  },

  /**
   * Forgot password
   */
  async forgotPassword(email) {
    const response = await apiHelpers.post('/auth/forgot-password', { email })
    return response.data
  },

  /**
   * Reset password
   */
  async resetPassword(token, passwordData) {
    const response = await apiHelpers.patch(`/auth/reset-password/${token}`, passwordData)
    return response.data
  },

  /**
   * Verify email
   */
  async verifyEmail(token) {
    const response = await apiHelpers.get(`/auth/verify-email/${token}`)
    return response.data
  },

  /**
   * Resend verification email
   */
  async resendVerificationEmail(email) {
    const response = await apiHelpers.post('/auth/resend-verification', { email })
    return response.data
  },

  /**
   * Refresh access token
   */
  async refreshToken() {
    const response = await apiHelpers.post('/auth/refresh-token')
    return response.data
  },

  /**
   * Delete user account
   */
  async deleteAccount() {
    const response = await apiHelpers.delete('/auth/delete-me')
    return response.data
  },

  /**
   * Google OAuth login
   */
  async googleLogin() {
    // This would typically redirect to Google OAuth
    window.location.href = `${import.meta.env.VITE_API_URL || 'http://localhost:3001/api'}/auth/google`
  },

  /**
   * Microsoft OAuth login
   */
  async microsoftLogin() {
    // This would typically redirect to Microsoft OAuth
    window.location.href = `${import.meta.env.VITE_API_URL || 'http://localhost:3001/api'}/auth/microsoft`
  }
}
