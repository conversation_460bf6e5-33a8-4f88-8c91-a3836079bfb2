import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  FireIcon,
  ShieldExclamationIcon,
  ClockIcon,
  CogIcon,
  ChartBarIcon,
  EyeSlashIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@services/api'
import { useSocket } from '@context/SocketContext'
import Loading from '@components/Loading'
import toast from 'react-hot-toast'

const Focus = () => {
  const [activeFocusSession, setActiveFocusSession] = useState(null)
  const [isActive, setIsActive] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [showSettings, setShowSettings] = useState(false)
  const [distractionCount, setDistractionCount] = useState(0)
  const [focusScore, setFocusScore] = useState(100)
  const [soundEnabled, setSoundEnabled] = useState(true)

  // Focus session settings
  const [focusSettings, setFocusSettings] = useState({
    duration: 25, // minutes
    sessionType: 'pomodoro',
    blockingEnabled: true,
    blockingMode: 'blacklist',
    blockedSites: [],
    allowedSites: [],
    breakDuration: 5,
    longBreakDuration: 15,
    longBreakInterval: 4,
    soundNotifications: true,
    visualNotifications: true,
    strictMode: false
  })

  const queryClient = useQueryClient()
  const { socket, isConnected } = useSocket()

  // Fetch active focus session
  const { data: activeFocus, isLoading: focusLoading } = useQuery(
    'activeFocusSession',
    () => apiHelpers.get('/focus/active'),
    {
      onSuccess: (response) => {
        const session = response.data.data.session
        if (session) {
          setActiveFocusSession(session)
          setIsActive(session.status === 'active')
          setDistractionCount(session.distractionCount || 0)
          setFocusScore(session.focusScore || 100)
        }
      },
      refetchInterval: 5000 // Refresh every 5 seconds when active
    }
  )

  // Fetch focus insights
  const { data: focusInsights } = useQuery(
    'focusInsights',
    () => apiHelpers.get('/focus/insights?days=7'),
    {
      select: (response) => response.data.data
    }
  )

  // Fetch recent focus sessions
  const { data: recentSessions } = useQuery(
    'recentFocusSessions',
    () => apiHelpers.get('/focus/sessions?limit=10'),
    {
      select: (response) => response.data.data.sessions
    }
  )

  // Start focus session mutation
  const startFocusMutation = useMutation(
    (settings) => apiHelpers.post('/focus/start', settings),
    {
      onSuccess: (response) => {
        const session = response.data.data.session
        setActiveFocusSession(session)
        setIsActive(true)
        setDistractionCount(0)
        setFocusScore(100)
        queryClient.invalidateQueries('activeFocusSession')
        toast.success('Focus session started!')
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to start focus session')
      }
    }
  )

  // Stop focus session mutation
  const stopFocusMutation = useMutation(
    () => apiHelpers.post('/focus/stop'),
    {
      onSuccess: () => {
        setActiveFocusSession(null)
        setIsActive(false)
        setTimeRemaining(0)
        setDistractionCount(0)
        setFocusScore(100)
        queryClient.invalidateQueries('activeFocusSession')
        queryClient.invalidateQueries('recentFocusSessions')
        queryClient.invalidateQueries('focusInsights')
        toast.success('Focus session completed!')
      }
    }
  )

  // Pause/Resume focus session mutation
  const pauseFocusMutation = useMutation(
    (action) => apiHelpers.post(`/focus/${action}`),
    {
      onSuccess: (response, action) => {
        setIsActive(action === 'resume')
        queryClient.invalidateQueries('activeFocusSession')
        toast.success(`Focus session ${action}d`)
      }
    }
  )

  // Timer effect
  useEffect(() => {
    let interval = null
    if (isActive && activeFocusSession) {
      interval = setInterval(() => {
        const startTime = new Date(activeFocusSession.startTime)
        const duration = activeFocusSession.duration * 60 * 1000 // Convert to milliseconds
        const elapsed = Date.now() - startTime.getTime()
        const remaining = Math.max(0, duration - elapsed)

        setTimeRemaining(Math.floor(remaining / 1000))

        // Auto-stop when time is up
        if (remaining <= 0 && isActive) {
          stopFocusMutation.mutate()
          if (soundEnabled) {
            // Play completion sound
            playNotificationSound()
          }
        }
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isActive, activeFocusSession, soundEnabled, stopFocusMutation])

  // Socket event listeners
  useEffect(() => {
    if (!socket || !isConnected) return

    const handleFocusStarted = (data) => {
      setActiveFocusSession(data)
      setIsActive(true)
      queryClient.invalidateQueries('activeFocusSession')
    }

    const handleFocusStopped = () => {
      setActiveFocusSession(null)
      setIsActive(false)
      setTimeRemaining(0)
      queryClient.invalidateQueries('activeFocusSession')
      queryClient.invalidateQueries('recentFocusSessions')
    }

    const handleFocusPaused = () => {
      setIsActive(false)
    }

    const handleFocusResumed = () => {
      setIsActive(true)
    }

    const handleDistractionDetected = (data) => {
      setDistractionCount(prev => prev + 1)
      setFocusScore(data.focusScore)
      if (soundEnabled) {
        playDistractionSound()
      }
    }

    socket.on('focus-started', handleFocusStarted)
    socket.on('focus-stopped', handleFocusStopped)
    socket.on('focus-paused', handleFocusPaused)
    socket.on('focus-resumed', handleFocusResumed)
    socket.on('distraction-detected', handleDistractionDetected)

    return () => {
      socket.off('focus-started', handleFocusStarted)
      socket.off('focus-stopped', handleFocusStopped)
      socket.off('focus-paused', handleFocusPaused)
      socket.off('focus-resumed', handleFocusResumed)
      socket.off('distraction-detected', handleDistractionDetected)
    }
  }, [socket, isConnected, queryClient, soundEnabled])

  // Sound effects
  const playNotificationSound = () => {
    const audio = new Audio('/sounds/focus-complete.mp3')
    audio.play().catch(() => {}) // Ignore errors if sound can't play
  }

  const playDistractionSound = () => {
    const audio = new Audio('/sounds/distraction-alert.mp3')
    audio.play().catch(() => {})
  }

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const formatDuration = (minutes) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return `${hours}h ${mins}m`
    }
    return `${minutes}m`
  }

  const handleStartFocus = () => {
    startFocusMutation.mutate({
      ...focusSettings,
      duration: focusSettings.duration
    })
  }

  const handleStopFocus = () => {
    stopFocusMutation.mutate()
  }

  const handlePauseFocus = () => {
    if (isActive) {
      pauseFocusMutation.mutate('pause')
    } else {
      pauseFocusMutation.mutate('resume')
    }
  }

  // Calculate progress percentage
  const progressPercentage = activeFocusSession && timeRemaining > 0
    ? ((activeFocusSession.duration * 60 - timeRemaining) / (activeFocusSession.duration * 60)) * 100
    : 0

  if (focusLoading) {
    return <Loading fullScreen />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Focus Mode
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Deep work sessions with distraction blocking
          </p>
        </div>

        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button
            onClick={() => setSoundEnabled(!soundEnabled)}
            className={`p-2 rounded-lg ${soundEnabled ? 'text-blue-600 bg-blue-100' : 'text-gray-400 bg-gray-100'}`}
          >
            {soundEnabled ? (
              <SpeakerWaveIcon className="h-5 w-5" />
            ) : (
              <SpeakerXMarkIcon className="h-5 w-5" />
            )}
          </button>

          <button
            onClick={() => setShowSettings(true)}
            className="btn btn-secondary flex items-center"
          >
            <CogIcon className="h-4 w-4 mr-2" />
            Settings
          </button>
        </div>
      </div>

      {/* Focus Timer */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="card"
      >
        <div className="card-body text-center">
          {activeFocusSession ? (
            // Active session display
            <div className="space-y-6">
              {/* Circular Progress Timer */}
              <div className="relative inline-flex items-center justify-center">
                <svg className="w-64 h-64 transform -rotate-90">
                  <circle
                    cx="128"
                    cy="128"
                    r="120"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="none"
                    className="text-gray-200 dark:text-gray-700"
                  />
                  <circle
                    cx="128"
                    cy="128"
                    r="120"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="none"
                    strokeDasharray={`${2 * Math.PI * 120}`}
                    strokeDashoffset={`${2 * Math.PI * 120 * (1 - progressPercentage / 100)}`}
                    className="text-blue-600 transition-all duration-1000 ease-in-out"
                    strokeLinecap="round"
                  />
                </svg>

                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="text-5xl font-mono font-bold text-gray-900 dark:text-gray-100">
                    {formatTime(timeRemaining)}
                  </div>
                  <div className="text-lg text-gray-500 dark:text-gray-400 mt-2">
                    {isActive ? 'Focus Time' : 'Paused'}
                  </div>
                  <div className="text-sm text-gray-400 mt-1">
                    {activeFocusSession.sessionType} • {activeFocusSession.duration}min
                  </div>
                </div>
              </div>

              {/* Session Stats */}
              <div className="grid grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {focusScore}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Focus Score
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {distractionCount}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Distractions
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round(progressPercentage)}%
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Complete
                  </div>
                </div>
              </div>

              {/* Controls */}
              <div className="flex justify-center space-x-4">
                <button
                  onClick={handlePauseFocus}
                  disabled={pauseFocusMutation.isLoading}
                  className="btn btn-secondary flex items-center"
                >
                  {isActive ? (
                    <>
                      <PauseIcon className="h-5 w-5 mr-2" />
                      Pause
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-5 w-5 mr-2" />
                      Resume
                    </>
                  )}
                </button>

                <button
                  onClick={handleStopFocus}
                  disabled={stopFocusMutation.isLoading}
                  className="btn btn-danger flex items-center"
                >
                  <StopIcon className="h-5 w-5 mr-2" />
                  Stop
                </button>
              </div>

              {/* Blocking Status */}
              {activeFocusSession.blockingEnabled && (
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <ShieldExclamationIcon className="h-4 w-4" />
                  <span>Website blocking is active</span>
                </div>
              )}
            </div>
          ) : (
            // Start session display
            <div className="space-y-6">
              <div className="text-center">
                <FireIcon className="mx-auto h-16 w-16 text-orange-500 mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  Ready to Focus?
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Start a focused work session with distraction blocking
                </p>
              </div>

              {/* Quick Settings */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Duration
                  </label>
                  <select
                    value={focusSettings.duration}
                    onChange={(e) => setFocusSettings(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                    className="form-select w-full"
                  >
                    <option value={15}>15 minutes</option>
                    <option value={25}>25 minutes</option>
                    <option value={45}>45 minutes</option>
                    <option value={60}>1 hour</option>
                    <option value={90}>1.5 hours</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Session Type
                  </label>
                  <select
                    value={focusSettings.sessionType}
                    onChange={(e) => setFocusSettings(prev => ({ ...prev, sessionType: e.target.value }))}
                    className="form-select w-full"
                  >
                    <option value="pomodoro">Pomodoro</option>
                    <option value="deep_work">Deep Work</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Blocking
                  </label>
                  <div className="flex items-center h-10">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={focusSettings.blockingEnabled}
                        onChange={(e) => setFocusSettings(prev => ({ ...prev, blockingEnabled: e.target.checked }))}
                        className="form-checkbox"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Enable blocking
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Start Button */}
              <button
                onClick={handleStartFocus}
                disabled={startFocusMutation.isLoading}
                className="btn btn-primary btn-lg flex items-center mx-auto"
              >
                <PlayIcon className="h-6 w-6 mr-3" />
                {startFocusMutation.isLoading ? 'Starting...' : 'Start Focus Session'}
              </button>
            </div>
          )}
        </div>
      </motion.div>

      {/* Focus Insights */}
      {focusInsights && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="card"
          >
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                This Week
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Sessions</span>
                  <span className="font-semibold">{focusInsights.totalSessions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                  <span className="font-semibold">{focusInsights.completedSessions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Focus Time</span>
                  <span className="font-semibold">{formatDuration(focusInsights.totalPlannedTime)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Avg Score</span>
                  <span className="font-semibold">{Math.round(focusInsights.averageFocusScore)}</span>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="card"
          >
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Performance
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completion Rate</span>
                  <span className="font-semibold">{Math.round(focusInsights.completionRate)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Best Streak</span>
                  <span className="font-semibold">{focusInsights.bestStreak} days</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Current Streak</span>
                  <span className="font-semibold">{focusInsights.currentStreak} days</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Improvement</span>
                  <span className="font-semibold text-green-600">+{focusInsights.improvement}%</span>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="card"
          >
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Peak Hours
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-3">
                {focusInsights.bestHours?.slice(0, 4).map((hour, index) => (
                  <div key={hour} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {hour}:00 - {hour + 1}:00
                    </span>
                    <div className="flex items-center">
                      <div className="w-12 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${100 - (index * 25)}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500">#{index + 1}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Recent Sessions */}
      {recentSessions && recentSessions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Recent Sessions
            </h3>
          </div>
          <div className="card-body p-0">
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {recentSessions.slice(0, 5).map((session) => (
                <div key={session.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        session.completed ? 'bg-green-400' : 'bg-orange-400'
                      }`} />

                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {session.sessionType} • {session.duration}min
                          </span>
                          {session.blockingEnabled && (
                            <ShieldExclamationIcon className="h-4 w-4 text-blue-500" />
                          )}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(session.startTime).toLocaleDateString()} at{' '}
                          {new Date(session.startTime).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                        Score: {session.focusScore || 0}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {session.distractionCount || 0} distractions
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {/* Settings Modal */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowSettings(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
                  Focus Settings
                </h3>

                <div className="space-y-6">
                  {/* Session Settings */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                      Session Settings
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Break Duration (minutes)
                        </label>
                        <input
                          type="number"
                          value={focusSettings.breakDuration}
                          onChange={(e) => setFocusSettings(prev => ({ ...prev, breakDuration: parseInt(e.target.value) }))}
                          className="form-input w-full"
                          min="1"
                          max="30"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Long Break Duration (minutes)
                        </label>
                        <input
                          type="number"
                          value={focusSettings.longBreakDuration}
                          onChange={(e) => setFocusSettings(prev => ({ ...prev, longBreakDuration: parseInt(e.target.value) }))}
                          className="form-input w-full"
                          min="10"
                          max="60"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Blocking Settings */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                      Website Blocking
                    </h4>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Blocking Mode
                        </label>
                        <select
                          value={focusSettings.blockingMode}
                          onChange={(e) => setFocusSettings(prev => ({ ...prev, blockingMode: e.target.value }))}
                          className="form-select w-full"
                        >
                          <option value="blacklist">Block specific sites</option>
                          <option value="whitelist">Allow only specific sites</option>
                        </select>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={focusSettings.strictMode}
                          onChange={(e) => setFocusSettings(prev => ({ ...prev, strictMode: e.target.checked }))}
                          className="form-checkbox"
                        />
                        <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Strict mode (harder to bypass)
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Notification Settings */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                      Notifications
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={focusSettings.soundNotifications}
                          onChange={(e) => setFocusSettings(prev => ({ ...prev, soundNotifications: e.target.checked }))}
                          className="form-checkbox"
                        />
                        <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Sound notifications
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={focusSettings.visualNotifications}
                          onChange={(e) => setFocusSettings(prev => ({ ...prev, visualNotifications: e.target.checked }))}
                          className="form-checkbox"
                        />
                        <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Visual notifications
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setShowSettings(false)}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      // Save settings logic would go here
                      setShowSettings(false)
                      toast.success('Settings saved!')
                    }}
                    className="btn btn-primary"
                  >
                    Save Settings
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export default Focus
