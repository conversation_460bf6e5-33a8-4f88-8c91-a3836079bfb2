/**
 * AI Task Prioritization Service
 * Machine learning algorithms for intelligent task ranking and scheduling
 */

const moment = require('moment');
const logger = require('../utils/logger');
const TimeSession = require('../models/TimeSession');
const { Op } = require('sequelize');

class AIPrioritizationService {
  constructor() {
    this.weights = {
      deadline: 0.3,
      importance: 0.25,
      effort: 0.2,
      userBehavior: 0.15,
      dependencies: 0.1
    };
  }

  /**
   * Prioritize tasks using AI algorithms
   */
  async prioritizeTasks(userId, tasks) {
    try {
      // Get user's historical data for behavior analysis
      const userBehavior = await this.analyzeUserBehavior(userId);
      const currentTime = moment();

      // Calculate priority scores for each task
      const prioritizedTasks = await Promise.all(
        tasks.map(async (task) => {
          const scores = await this.calculateTaskScores(task, userBehavior, currentTime);
          const finalScore = this.calculateWeightedScore(scores);
          
          return {
            ...task,
            priorityScore: finalScore,
            aiRecommendations: this.generateRecommendations(task, scores, userBehavior),
            estimatedDuration: await this.estimateTaskDuration(task, userBehavior),
            optimalTimeSlot: this.findOptimalTimeSlot(task, userBehavior)
          };
        })
      );

      // Sort by priority score (highest first)
      return prioritizedTasks.sort((a, b) => b.priorityScore - a.priorityScore);
    } catch (error) {
      logger.error('Error in AI task prioritization:', error);
      throw error;
    }
  }

  /**
   * Calculate individual scores for different factors
   */
  async calculateTaskScores(task, userBehavior, currentTime) {
    const scores = {};

    // Deadline urgency score (0-100)
    scores.deadline = this.calculateDeadlineScore(task.dueDate, currentTime);

    // Importance score based on priority level (0-100)
    scores.importance = this.calculateImportanceScore(task.priority);

    // Effort score based on estimated complexity (0-100, inverted - easier tasks score higher)
    scores.effort = this.calculateEffortScore(task.estimatedHours, task.complexity);

    // User behavior score based on historical patterns (0-100)
    scores.userBehavior = this.calculateBehaviorScore(task, userBehavior);

    // Dependencies score (0-100)
    scores.dependencies = this.calculateDependencyScore(task.dependencies || []);

    return scores;
  }

  /**
   * Calculate deadline urgency score
   */
  calculateDeadlineScore(dueDate, currentTime) {
    if (!dueDate) return 30; // Default score for tasks without deadlines

    const hoursUntilDeadline = moment(dueDate).diff(currentTime, 'hours');
    
    if (hoursUntilDeadline < 0) return 100; // Overdue tasks get maximum urgency
    if (hoursUntilDeadline < 24) return 90; // Due within 24 hours
    if (hoursUntilDeadline < 72) return 70; // Due within 3 days
    if (hoursUntilDeadline < 168) return 50; // Due within a week
    if (hoursUntilDeadline < 720) return 30; // Due within a month
    
    return 10; // Due in more than a month
  }

  /**
   * Calculate importance score based on priority level
   */
  calculateImportanceScore(priority) {
    const priorityScores = {
      'urgent': 100,
      'high': 80,
      'medium': 50,
      'low': 20
    };
    
    return priorityScores[priority] || 50;
  }

  /**
   * Calculate effort score (inverted - easier tasks get higher scores)
   */
  calculateEffortScore(estimatedHours, complexity) {
    let effortScore = 50; // Default

    // Factor in estimated hours
    if (estimatedHours) {
      if (estimatedHours <= 1) effortScore += 30;
      else if (estimatedHours <= 4) effortScore += 20;
      else if (estimatedHours <= 8) effortScore += 10;
      else effortScore -= 10;
    }

    // Factor in complexity
    const complexityScores = {
      'low': 20,
      'medium': 0,
      'high': -20,
      'very_high': -40
    };
    
    effortScore += complexityScores[complexity] || 0;
    
    return Math.max(0, Math.min(100, effortScore));
  }

  /**
   * Calculate user behavior score based on historical patterns
   */
  calculateBehaviorScore(task, userBehavior) {
    let behaviorScore = 50; // Default

    // Factor in user's productive hours
    const currentHour = moment().hour();
    if (userBehavior.peakHours.includes(currentHour)) {
      behaviorScore += 20;
    }

    // Factor in task category preferences
    if (task.category && userBehavior.preferredCategories.includes(task.category)) {
      behaviorScore += 15;
    }

    // Factor in completion patterns
    if (task.estimatedHours && userBehavior.averageSessionLength) {
      const sessionFit = Math.abs(task.estimatedHours * 60 - userBehavior.averageSessionLength) / 60;
      if (sessionFit < 0.5) behaviorScore += 10; // Task fits well with user's session patterns
    }

    return Math.max(0, Math.min(100, behaviorScore));
  }

  /**
   * Calculate dependency score
   */
  calculateDependencyScore(dependencies) {
    if (dependencies.length === 0) return 80; // No dependencies = higher score
    
    const completedDependencies = dependencies.filter(dep => dep.status === 'completed').length;
    const completionRatio = completedDependencies / dependencies.length;
    
    return Math.round(completionRatio * 100);
  }

  /**
   * Calculate weighted final score
   */
  calculateWeightedScore(scores) {
    return Math.round(
      scores.deadline * this.weights.deadline +
      scores.importance * this.weights.importance +
      scores.effort * this.weights.effort +
      scores.userBehavior * this.weights.userBehavior +
      scores.dependencies * this.weights.dependencies
    );
  }

  /**
   * Analyze user behavior patterns from historical data
   */
  async analyzeUserBehavior(userId) {
    try {
      const thirtyDaysAgo = moment().subtract(30, 'days').toDate();
      
      const sessions = await TimeSession.findAll({
        where: {
          userId,
          startTime: { [Op.gte]: thirtyDaysAgo },
          endTime: { [Op.not]: null }
        }
      });

      // Analyze peak productivity hours
      const hourlyProductivity = Array.from({ length: 24 }, () => ({ sessions: 0, totalScore: 0 }));
      
      sessions.forEach(session => {
        const hour = moment(session.startTime).hour();
        hourlyProductivity[hour].sessions++;
        hourlyProductivity[hour].totalScore += session.getProductivityScore();
      });

      const peakHours = hourlyProductivity
        .map((data, hour) => ({
          hour,
          avgScore: data.sessions > 0 ? data.totalScore / data.sessions : 0,
          sessions: data.sessions
        }))
        .filter(data => data.sessions >= 3) // Minimum 3 sessions for reliability
        .sort((a, b) => b.avgScore - a.avgScore)
        .slice(0, 4) // Top 4 productive hours
        .map(data => data.hour);

      // Analyze preferred categories
      const categoryStats = {};
      sessions.forEach(session => {
        if (session.category) {
          if (!categoryStats[session.category]) {
            categoryStats[session.category] = { time: 0, score: 0, count: 0 };
          }
          categoryStats[session.category].time += session.duration || 0;
          categoryStats[session.category].score += session.getProductivityScore();
          categoryStats[session.category].count++;
        }
      });

      const preferredCategories = Object.entries(categoryStats)
        .map(([category, stats]) => ({
          category,
          avgScore: stats.count > 0 ? stats.score / stats.count : 0,
          totalTime: stats.time
        }))
        .filter(data => data.totalTime > 3600) // At least 1 hour total
        .sort((a, b) => b.avgScore - a.avgScore)
        .slice(0, 5) // Top 5 categories
        .map(data => data.category);

      // Calculate average session length
      const completedSessions = sessions.filter(s => s.duration);
      const averageSessionLength = completedSessions.length > 0
        ? completedSessions.reduce((sum, s) => sum + s.duration, 0) / completedSessions.length / 60 // Convert to minutes
        : 60; // Default 60 minutes

      return {
        peakHours,
        preferredCategories,
        averageSessionLength,
        totalSessions: sessions.length,
        averageProductivityScore: sessions.length > 0
          ? sessions.reduce((sum, s) => sum + s.getProductivityScore(), 0) / sessions.length
          : 50
      };
    } catch (error) {
      logger.error('Error analyzing user behavior:', error);
      return {
        peakHours: [9, 10, 14, 15], // Default productive hours
        preferredCategories: [],
        averageSessionLength: 60,
        totalSessions: 0,
        averageProductivityScore: 50
      };
    }
  }

  /**
   * Generate AI recommendations for a task
   */
  generateRecommendations(task, scores, userBehavior) {
    const recommendations = [];

    // Deadline-based recommendations
    if (scores.deadline > 80) {
      recommendations.push({
        type: 'urgency',
        message: 'This task is urgent and should be completed soon.',
        priority: 'high'
      });
    }

    // Effort-based recommendations
    if (scores.effort > 70) {
      recommendations.push({
        type: 'quick_win',
        message: 'This is a quick task that can be completed easily.',
        priority: 'medium'
      });
    }

    // Time-based recommendations
    const currentHour = moment().hour();
    if (userBehavior.peakHours.includes(currentHour)) {
      recommendations.push({
        type: 'optimal_time',
        message: 'Now is one of your most productive hours - great time to tackle this task.',
        priority: 'medium'
      });
    }

    // Complexity recommendations
    if (task.complexity === 'high' || task.complexity === 'very_high') {
      recommendations.push({
        type: 'break_down',
        message: 'Consider breaking this complex task into smaller subtasks.',
        priority: 'low'
      });
    }

    return recommendations;
  }

  /**
   * Estimate task duration based on historical data
   */
  async estimateTaskDuration(task, userBehavior) {
    // If task already has an estimate, use it as base
    if (task.estimatedHours) {
      return task.estimatedHours * 60; // Convert to minutes
    }

    // Use complexity and category to estimate
    const complexityMultipliers = {
      'low': 0.5,
      'medium': 1.0,
      'high': 2.0,
      'very_high': 4.0
    };

    const baseTime = userBehavior.averageSessionLength || 60; // Default 60 minutes
    const multiplier = complexityMultipliers[task.complexity] || 1.0;

    return Math.round(baseTime * multiplier);
  }

  /**
   * Find optimal time slot for task execution
   */
  findOptimalTimeSlot(task, userBehavior) {
    const currentHour = moment().hour();
    
    // If it's currently a peak hour, suggest now
    if (userBehavior.peakHours.includes(currentHour)) {
      return {
        suggestion: 'now',
        reason: 'Current time matches your peak productivity hours'
      };
    }

    // Find next peak hour
    const nextPeakHour = userBehavior.peakHours.find(hour => hour > currentHour) || 
                        userBehavior.peakHours[0]; // Next day's first peak hour

    const nextPeakTime = moment().hour(nextPeakHour).minute(0);
    if (nextPeakHour <= currentHour) {
      nextPeakTime.add(1, 'day');
    }

    return {
      suggestion: nextPeakTime.format('YYYY-MM-DD HH:mm'),
      reason: `Scheduled for ${nextPeakTime.format('h:mm A')} - one of your most productive hours`
    };
  }

  /**
   * Smart scheduling algorithm
   */
  async scheduleOptimalTasks(userId, tasks, timeSlots) {
    try {
      const userBehavior = await this.analyzeUserBehavior(userId);
      const prioritizedTasks = await this.prioritizeTasks(userId, tasks);
      
      const schedule = [];
      let availableSlots = [...timeSlots];

      for (const task of prioritizedTasks) {
        const estimatedDuration = await this.estimateTaskDuration(task, userBehavior);
        
        // Find best time slot for this task
        const bestSlot = this.findBestTimeSlot(task, availableSlots, userBehavior, estimatedDuration);
        
        if (bestSlot) {
          schedule.push({
            task,
            timeSlot: bestSlot,
            estimatedDuration,
            confidence: this.calculateSchedulingConfidence(task, bestSlot, userBehavior)
          });
          
          // Remove or adjust the used time slot
          availableSlots = this.updateAvailableSlots(availableSlots, bestSlot, estimatedDuration);
        }
      }

      return schedule;
    } catch (error) {
      logger.error('Error in smart scheduling:', error);
      throw error;
    }
  }

  /**
   * Find the best time slot for a task
   */
  findBestTimeSlot(task, availableSlots, userBehavior, estimatedDuration) {
    let bestSlot = null;
    let bestScore = -1;

    for (const slot of availableSlots) {
      const slotStart = moment(slot.start);
      const slotEnd = moment(slot.end);
      const slotDuration = slotEnd.diff(slotStart, 'minutes');

      // Check if task fits in this slot
      if (slotDuration < estimatedDuration) continue;

      // Calculate slot score based on various factors
      let score = 0;

      // Peak hours bonus
      const slotHour = slotStart.hour();
      if (userBehavior.peakHours.includes(slotHour)) {
        score += 30;
      }

      // Deadline proximity bonus
      if (task.dueDate) {
        const hoursUntilDeadline = moment(task.dueDate).diff(slotStart, 'hours');
        if (hoursUntilDeadline > 0 && hoursUntilDeadline < 48) {
          score += 20;
        }
      }

      // Task complexity vs slot length match
      const complexityFit = this.calculateComplexityFit(task.complexity, slotDuration);
      score += complexityFit;

      if (score > bestScore) {
        bestScore = score;
        bestSlot = slot;
      }
    }

    return bestSlot;
  }

  /**
   * Calculate how well task complexity fits with available time
   */
  calculateComplexityFit(complexity, availableMinutes) {
    const idealDurations = {
      'low': 30,
      'medium': 90,
      'high': 180,
      'very_high': 300
    };

    const idealDuration = idealDurations[complexity] || 90;
    const difference = Math.abs(availableMinutes - idealDuration);
    
    // Return score from 0-20 based on how close the fit is
    return Math.max(0, 20 - (difference / 10));
  }

  /**
   * Update available time slots after scheduling a task
   */
  updateAvailableSlots(slots, usedSlot, duration) {
    return slots.map(slot => {
      if (slot.id === usedSlot.id) {
        const slotStart = moment(slot.start);
        const newStart = slotStart.clone().add(duration, 'minutes');
        
        // If there's remaining time in the slot, return the updated slot
        if (newStart.isBefore(moment(slot.end))) {
          return {
            ...slot,
            start: newStart.toISOString()
          };
        }
        // Otherwise, remove the slot (return null and filter later)
        return null;
      }
      return slot;
    }).filter(Boolean);
  }

  /**
   * Calculate confidence score for scheduling decision
   */
  calculateSchedulingConfidence(task, timeSlot, userBehavior) {
    let confidence = 50; // Base confidence

    // Higher confidence for peak hours
    const slotHour = moment(timeSlot.start).hour();
    if (userBehavior.peakHours.includes(slotHour)) {
      confidence += 20;
    }

    // Higher confidence for preferred categories
    if (task.category && userBehavior.preferredCategories.includes(task.category)) {
      confidence += 15;
    }

    // Higher confidence for tasks with clear estimates
    if (task.estimatedHours) {
      confidence += 10;
    }

    // Lower confidence for very complex tasks
    if (task.complexity === 'very_high') {
      confidence -= 15;
    }

    return Math.max(0, Math.min(100, confidence));
  }
}

module.exports = new AIPrioritizationService();
