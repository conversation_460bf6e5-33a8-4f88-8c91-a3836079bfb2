/**
 * Analytics Routes
 * Handles productivity analytics and insights generation
 */

const express = require('express');
const analyticsController = require('../controllers/analyticsController');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(authenticate);

// Productivity analytics
router.get('/productivity', analyticsController.getProductivityAnalytics);
router.get('/productivity/trends', analyticsController.getProductivityTrends);
router.get('/productivity/comparison', analyticsController.getProductivityComparison);

// Time analytics
router.get('/time/distribution', analyticsController.getTimeDistribution);
router.get('/time/patterns', analyticsController.getTimePatterns);
router.get('/time/efficiency', analyticsController.getTimeEfficiency);

// Focus analytics
router.get('/focus/sessions', analyticsController.getFocusSessions);
router.get('/focus/distractions', analyticsController.getDistractionAnalysis);
router.get('/focus/patterns', analyticsController.getFocusPatterns);

// Task analytics
router.get('/tasks/completion', analyticsController.getTaskCompletion);
router.get('/tasks/categories', analyticsController.getTaskCategoryAnalysis);
router.get('/tasks/priority', analyticsController.getTaskPriorityAnalysis);

// Energy and performance analytics
router.get('/energy/levels', analyticsController.getEnergyLevels);
router.get('/energy/optimal-times', analyticsController.getOptimalTimes);
router.get('/performance/peaks', analyticsController.getPerformancePeaks);

// Reports generation
router.get('/reports/daily', analyticsController.getDailyReport);
router.get('/reports/weekly', analyticsController.getWeeklyReport);
router.get('/reports/monthly', analyticsController.getMonthlyReport);
router.post('/reports/custom', analyticsController.generateCustomReport);

// Insights and recommendations
router.get('/insights', analyticsController.getInsights);
router.get('/recommendations', analyticsController.getRecommendations);

module.exports = router;
