/**
 * TimeWarp Browser Extension - Content Script
 * Handles website blocking, distraction detection, and user activity monitoring
 */

let focusSession = null;
let isBlocked = false;
let blockOverlay = null;
let activityTracker = null;

// Initialize content script
(function() {
  console.log('TimeWarp content script loaded');
  
  // Check if current site should be blocked
  checkBlockingStatus();
  
  // Set up activity tracking
  setupActivityTracking();
  
  // Listen for messages from background script
  chrome.runtime.onMessage.addListener(handleMessage);
})();

function handleMessage(request, sender, sendResponse) {
  switch (request.action) {
    case 'focusModeStarted':
      focusSession = request.session;
      checkBlockingStatus();
      break;
      
    case 'focusModeEnded':
      focusSession = null;
      removeBlockOverlay();
      break;
      
    case 'checkActivity':
      sendResponse({
        isActive: document.hasFocus(),
        scrollPosition: window.scrollY,
        timestamp: Date.now()
      });
      break;
  }
}

async function checkBlockingStatus() {
  if (!focusSession) return;
  
  try {
    const response = await chrome.runtime.sendMessage({
      action: 'checkBlocked',
      url: window.location.href
    });
    
    if (response.blocked) {
      showBlockOverlay();
    } else {
      removeBlockOverlay();
    }
  } catch (error) {
    console.error('Error checking blocking status:', error);
  }
}

function showBlockOverlay() {
  if (blockOverlay) return;
  
  isBlocked = true;
  
  // Create blocking overlay
  blockOverlay = document.createElement('div');
  blockOverlay.id = 'timewarp-block-overlay';
  blockOverlay.innerHTML = `
    <div class="timewarp-block-content">
      <div class="timewarp-block-icon">🎯</div>
      <h1>Focus Mode Active</h1>
      <p>This website is blocked during your focus session.</p>
      <div class="timewarp-block-timer" id="timewarp-timer">
        <span id="timewarp-time-remaining">Loading...</span>
      </div>
      <div class="timewarp-block-actions">
        <button id="timewarp-break-rules" class="timewarp-btn-secondary">
          Break Rules (Not Recommended)
        </button>
        <button id="timewarp-end-session" class="timewarp-btn-primary">
          End Focus Session
        </button>
      </div>
      <div class="timewarp-motivation">
        <p>"The successful warrior is the average person with laser-like focus." - Bruce Lee</p>
      </div>
    </div>
  `;
  
  // Add styles
  const styles = `
    <style>
      #timewarp-block-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        z-index: 2147483647 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        color: white !important;
      }
      
      .timewarp-block-content {
        text-align: center !important;
        max-width: 500px !important;
        padding: 40px !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 20px !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
      }
      
      .timewarp-block-icon {
        font-size: 64px !important;
        margin-bottom: 20px !important;
      }
      
      .timewarp-block-content h1 {
        font-size: 32px !important;
        margin: 0 0 16px 0 !important;
        font-weight: 600 !important;
        color: white !important;
      }
      
      .timewarp-block-content p {
        font-size: 18px !important;
        margin: 0 0 30px 0 !important;
        opacity: 0.9 !important;
        color: white !important;
      }
      
      .timewarp-block-timer {
        background: rgba(255, 255, 255, 0.2) !important;
        padding: 20px !important;
        border-radius: 12px !important;
        margin: 30px 0 !important;
      }
      
      #timewarp-time-remaining {
        font-size: 24px !important;
        font-weight: 600 !important;
        font-family: 'Courier New', monospace !important;
      }
      
      .timewarp-block-actions {
        display: flex !important;
        gap: 16px !important;
        justify-content: center !important;
        margin: 30px 0 !important;
      }
      
      .timewarp-btn-primary, .timewarp-btn-secondary {
        padding: 12px 24px !important;
        border: none !important;
        border-radius: 8px !important;
        font-size: 16px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.2s !important;
      }
      
      .timewarp-btn-primary {
        background: #22c55e !important;
        color: white !important;
      }
      
      .timewarp-btn-primary:hover {
        background: #16a34a !important;
        transform: translateY(-1px) !important;
      }
      
      .timewarp-btn-secondary {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
      }
      
      .timewarp-btn-secondary:hover {
        background: rgba(255, 255, 255, 0.3) !important;
      }
      
      .timewarp-motivation {
        margin-top: 30px !important;
        padding-top: 20px !important;
        border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
      }
      
      .timewarp-motivation p {
        font-style: italic !important;
        font-size: 14px !important;
        opacity: 0.8 !important;
        margin: 0 !important;
      }
    </style>
  `;
  
  blockOverlay.innerHTML = styles + blockOverlay.innerHTML;
  document.body.appendChild(blockOverlay);
  
  // Set up event listeners
  document.getElementById('timewarp-break-rules').addEventListener('click', breakRules);
  document.getElementById('timewarp-end-session').addEventListener('click', endFocusSession);
  
  // Start timer
  updateTimer();
  setInterval(updateTimer, 1000);
  
  // Record distraction
  recordDistraction('website_blocked', window.location.hostname, true);
}

function removeBlockOverlay() {
  if (blockOverlay) {
    blockOverlay.remove();
    blockOverlay = null;
    isBlocked = false;
  }
}

function updateTimer() {
  if (!focusSession || !blockOverlay) return;
  
  const timerElement = document.getElementById('timewarp-time-remaining');
  if (!timerElement) return;
  
  // Calculate remaining time (this would come from the session data)
  const sessionDuration = focusSession.data.session.duration * 60 * 1000; // Convert to milliseconds
  const sessionStart = new Date(focusSession.data.session.startTime).getTime();
  const elapsed = Date.now() - sessionStart;
  const remaining = Math.max(0, sessionDuration - elapsed);
  
  if (remaining === 0) {
    timerElement.textContent = 'Session Complete!';
    return;
  }
  
  const minutes = Math.floor(remaining / (60 * 1000));
  const seconds = Math.floor((remaining % (60 * 1000)) / 1000);
  
  timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

async function breakRules() {
  // Record that user chose to break the rules
  await recordDistraction('rule_break', window.location.hostname, false);
  
  // Show warning
  if (confirm('Breaking focus rules may impact your productivity score. Are you sure?')) {
    removeBlockOverlay();
    
    // Show temporary warning banner
    showWarningBanner();
  }
}

function showWarningBanner() {
  const banner = document.createElement('div');
  banner.id = 'timewarp-warning-banner';
  banner.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #ef4444;
      color: white;
      padding: 12px;
      text-align: center;
      z-index: 1000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
    ">
      ⚠️ Focus Mode Active - This site may impact your productivity
      <button onclick="this.parentElement.parentElement.remove()" style="
        background: none;
        border: none;
        color: white;
        margin-left: 16px;
        cursor: pointer;
        font-size: 16px;
      ">×</button>
    </div>
  `;
  
  document.body.appendChild(banner);
  
  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (banner.parentElement) {
      banner.remove();
    }
  }, 10000);
}

async function endFocusSession() {
  try {
    await chrome.runtime.sendMessage({ action: 'stopFocusMode' });
    removeBlockOverlay();
  } catch (error) {
    console.error('Error ending focus session:', error);
  }
}

async function recordDistraction(type, source, blocked) {
  try {
    await chrome.runtime.sendMessage({
      action: 'recordDistraction',
      data: {
        type,
        source,
        blocked,
        timestamp: new Date().toISOString(),
        metadata: {
          url: window.location.href,
          title: document.title
        }
      }
    });
  } catch (error) {
    console.error('Error recording distraction:', error);
  }
}

function setupActivityTracking() {
  // Track user interactions for productivity analysis
  let lastActivity = Date.now();
  let keystrokes = 0;
  let mouseClicks = 0;
  let scrollEvents = 0;
  
  // Keyboard activity
  document.addEventListener('keydown', () => {
    keystrokes++;
    lastActivity = Date.now();
  });
  
  // Mouse activity
  document.addEventListener('click', () => {
    mouseClicks++;
    lastActivity = Date.now();
  });
  
  // Scroll activity
  document.addEventListener('scroll', () => {
    scrollEvents++;
    lastActivity = Date.now();
  });
  
  // Periodic activity reporting
  activityTracker = setInterval(() => {
    const now = Date.now();
    const idleTime = now - lastActivity;
    
    // Only report if there was activity or if we need to update idle status
    if (keystrokes > 0 || mouseClicks > 0 || scrollEvents > 0 || idleTime > 60000) {
      chrome.runtime.sendMessage({
        action: 'updateActivity',
        data: {
          keystrokes,
          mouseClicks,
          scrollEvents,
          idleTime,
          url: window.location.href,
          title: document.title,
          timestamp: now
        }
      }).catch(() => {}); // Ignore errors
      
      // Reset counters
      keystrokes = 0;
      mouseClicks = 0;
      scrollEvents = 0;
    }
  }, 30000); // Report every 30 seconds
}

// Clean up on page unload
window.addEventListener('beforeunload', () => {
  if (activityTracker) {
    clearInterval(activityTracker);
  }
});
